# UI Component Library

Comprehensive reusable component library providing consistent styling, behavior, and accessibility across the application.

## Overview

The UI library provides a complete set of reusable components built with Tailwind CSS and designed for the DSAT16 platform. Components follow consistent design patterns, accessibility standards, and include specialized components for educational features.

## Architecture

```
ui/
├── index.ts                 # Main component exports
├── Button.svelte           # Primary action buttons
├── LoadingButton.svelte    # Button with loading states
├── Input.svelte            # Text input fields
├── Checkbox.svelte         # Custom checkbox component
├── Dropdown.svelte         # Select dropdown component
├── Slider.svelte           # Range slider input
├── PopUp.svelte            # Modal dialog component
├── HelpIcon.svelte         # Tooltip help icons
├── PracticeQuestion.svelte # Reusable question display
├── QuestionHeader.svelte   # Question metadata display
├── PricingTimeline.svelte  # Subscription timeline
├── WaitlistForm.svelte     # Email capture forms
├── MarkBarUI.svelte        # Question navigation bar
├── typography/             # Text components (H1-H5, P1-P3)
├── pricing-cards/          # Subscription plan cards
└── social-icons/           # Social media icons
```

## Core Components

### Button System
```svelte
<!-- Button.svelte - Primary action component -->
<script>
  let { 
    variant = 'primary',
    size = 'medium', 
    disabled = false,
    loading = false,
    onclick,
    href,
    children 
  } = $props();
  
  // Button variants: primary, secondary, ghost, danger
  // Sizes: small, medium, large
</script>

<!-- Usage examples -->
<Button variant="primary" size="large" onclick={handleSubmit}>
  Get Started
</Button>

<Button variant="ghost" href="/pricing">
  View Pricing
</Button>

<Button variant="danger" disabled={loading}>
  Delete Account
</Button>
```

### Form Components
```svelte
<!-- Input.svelte - Text input with validation -->
<script>
  import { HelpIcon } from '$lib/ui';
  
  let { 
    value = '',
    type = 'text',
    placeholder = '',
    label,
    error,
    helpText,
    required = false,
    disabled = false,
    onchange
  } = $props();
</script>

<div class="input-group">
  {#if label}
    <label class="input-label">
      {label}
      {#if required}<span class="required">*</span>{/if}
      {#if helpText}<HelpIcon tooltip={helpText} />{/if}
    </label>
  {/if}
  
  <input
    {type}
    {placeholder}
    {required}
    {disabled}
    bind:value
    onchange={onchange}
    class="input-field"
    class:error={error}
  />
  
  {#if error}
    <span class="error-message">{error}</span>
  {/if}
</div>

<!-- Usage -->
<Input
  label="Email Address"
  type="email"
  placeholder="Enter your email"
  helpText="We'll never share your email"
  error={emailError}
  bind:value={email}
  required
/>
```

### Dropdown Component
```svelte
<!-- Dropdown.svelte - Select dropdown -->
<script>
  let { 
    options = [],
    selected = '',
    placeholder = 'Select an option',
    label,
    onselect
  } = $props();
  
  let open = $state(false);
</script>

<div class="dropdown" class:open>
  {#if label}
    <label class="dropdown-label">{label}</label>
  {/if}
  
  <button 
    class="dropdown-trigger"
    onclick={() => open = !open}
  >
    {selected || placeholder}
    <svg class="dropdown-arrow" class:rotated={open}>...</svg>
  </button>
  
  {#if open}
    <div class="dropdown-menu">
      {#each options as option}
        <button 
          class="dropdown-item"
          onclick={() => { 
            selected = option.value; 
            onselect?.(option.value);
            open = false; 
          }}
        >
          {option.label}
        </button>
      {/each}
    </div>
  {/if}
</div>

<!-- Usage -->
<Dropdown
  label="Difficulty Level"
  options={[
    { label: 'Easy', value: 'easy' },
    { label: 'Medium', value: 'medium' },
    { label: 'Hard', value: 'hard' }
  ]}
  bind:selected={difficulty}
  onselect={handleDifficultyChange}
/>
```

## Typography System

### Text Components
```svelte
<!-- typography/H1.svelte through H5.svelte -->
<script>
  let { 
    children,
    class: className = '',
    color = 'default'
  } = $props();
  
  // Colors: default, primary, secondary, muted, error, success
</script>

<h1 class="heading-1 {className}" class:color-{color}>
  {children}
</h1>

<!-- Paragraph components P1.svelte through P3.svelte -->
<script>
  let { children, class: className = '' } = $props();
</script>

<p class="paragraph-1 {className}">
  {children}
</p>

<!-- Usage -->
<H1 color="primary">Welcome to DSAT16</H1>
<H2>Start Your SAT Journey</H2>
<P1>Comprehensive test preparation platform</P1>
<P2 class="text-center">Designed for success</P2>
```

### Wrapper Components
```svelte
<!-- P1Wrapper.svelte - Layout wrapper for consistent spacing -->
<script>
  let { 
    children,
    class: className = '',
    spacing = 'normal' // tight, normal, loose
  } = $props();
</script>

<div class="text-wrapper spacing-{spacing} {className}">
  {children}
</div>

<!-- Usage -->
<P1Wrapper spacing="loose">
  <P1>First paragraph with more space</P1>
  <P1>Second paragraph with more space</P1>
</P1Wrapper>
```

## Educational Components

### Practice Question Display
```svelte
<!-- PracticeQuestion.svelte - Reusable question component -->
<script>
  import { QuestionHeader } from '$lib/ui';
  
  let { 
    question,
    userAnswer = '',
    showExplanation = false,
    onAnswerChange,
    onSubmit,
    disabled = false
  } = $props();
  
  let letters = ['A', 'B', 'C', 'D'];
</script>

<div class="practice-question">
  <QuestionHeader 
    difficulty={question.difficulty}
    type={question.questionType}
    number={question.number}
  />
  
  <div class="question-content">
    <p class="question-text">{@html question.question}</p>
    
    {#if question.passage}
      <div class="passage">
        <p>{@html question.passage}</p>
      </div>
    {/if}
  </div>
  
  <div class="answer-choices">
    {#each question.choices as choice, index}
      <label class="choice-option">
        <input 
          type="radio"
          name="answer"
          value={letters[index]}
          checked={userAnswer === letters[index]}
          onchange={() => onAnswerChange?.(letters[index])}
          {disabled}
        />
        <span class="choice-letter">{letters[index]}</span>
        <span class="choice-text">{@html choice}</span>
      </label>
    {/each}
  </div>
  
  {#if !disabled}
    <Button 
      variant="primary" 
      onclick={onSubmit}
      disabled={!userAnswer}
    >
      Submit Answer
    </Button>
  {/if}
  
  {#if showExplanation}
    <div class="explanation">
      <h4>Explanation:</h4>
      <p>{@html question.explanation}</p>
    </div>
  {/if}
</div>
```

### Question Navigation
```svelte
<!-- MarkBarUI.svelte - Question navigation bar -->
<script>
  let { 
    questions = [],
    currentIndex = 0,
    answers = {},
    markedQuestions = {},
    onQuestionSelect
  } = $props();
</script>

<div class="mark-bar">
  <div class="question-numbers">
    {#each questions as question, index}
      <button 
        class="question-marker"
        class:current={index === currentIndex}
        class:answered={answers[question.id]}
        class:marked={markedQuestions[question.id]}
        onclick={() => onQuestionSelect?.(index)}
      >
        {index + 1}
      </button>
    {/each}
  </div>
  
  <div class="legend">
    <span class="legend-item">
      <span class="marker current"></span>
      Current
    </span>
    <span class="legend-item">
      <span class="marker answered"></span>
      Answered
    </span>
    <span class="legend-item">
      <span class="marker marked"></span>
      Marked
    </span>
  </div>
</div>
```

## Specialized Components

### Modal System
```svelte
<!-- PopUp.svelte - Modal dialog -->
<script>
  let { 
    open = false,
    onClose,
    title,
    size = 'medium', // small, medium, large, full
    children,
    showCloseButton = true
  } = $props();
  
  // Handle ESC key and outside click
  function handleKeydown(event) {
    if (event.key === 'Escape' && open) {
      onClose?.();
    }
  }
  
  function handleOutsideClick(event) {
    if (event.target === event.currentTarget) {
      onClose?.();
    }
  }
</script>

<svelte:window on:keydown={handleKeydown} />

{#if open}
  <div 
    class="modal-overlay" 
    onclick={handleOutsideClick}
    transition:fade={{ duration: 200 }}
  >
    <div 
      class="modal-content size-{size}"
      transition:fly={{ y: 50, duration: 300 }}
    >
      {#if title || showCloseButton}
        <div class="modal-header">
          {#if title}
            <h3 class="modal-title">{title}</h3>
          {/if}
          
          {#if showCloseButton}
            <button class="modal-close" onclick={onClose}>×</button>
          {/if}
        </div>
      {/if}
      
      <div class="modal-body">
        {children}
      </div>
    </div>
  </div>
{/if}

<!-- Usage -->
<PopUp
  title="Confirm Action"
  size="small"
  onClose={() => showModal = false}
>
  <p>Are you sure you want to delete this item?</p>
  <div class="modal-actions">
    <Button variant="ghost" onclick={() => showModal = false}>
      Cancel
    </Button>
    <Button variant="danger" onclick={handleDelete}>
      Delete
    </Button>
  </div>
</PopUp>
```

### Help System
```svelte
<!-- HelpIcon.svelte - Contextual help tooltips -->
<script>
  let { 
    tooltip,
    position = 'top', // top, bottom, left, right
    size = 'small'    // small, medium
  } = $props();
  
  let showTooltip = $state(false);
</script>

<div class="help-icon-container">
  <button 
    class="help-icon size-{size}"
    onmouseenter={() => showTooltip = true}
    onmouseleave={() => showTooltip = false}
    onfocus={() => showTooltip = true}
    onblur={() => showTooltip = false}
  >
    ?
  </button>
  
  {#if showTooltip && tooltip}
    <div class="tooltip position-{position}">
      {tooltip}
    </div>
  {/if}
</div>

<!-- Usage -->
<label>
  Password 
  <HelpIcon 
    tooltip="Must be at least 8 characters with uppercase, lowercase, and numbers"
    position="right"
  />
</label>
```

## Pricing Components

### Subscription Cards
```svelte
<!-- pricing-cards/PricingCard.svelte -->
<script>
  import { Button } from '$lib/ui';
  
  let { 
    title,
    price,
    period = 'month',
    features = [],
    popular = false,
    buttonText = 'Get Started',
    onSelect
  } = $props();
</script>

<div class="pricing-card" class:popular>
  {#if popular}
    <div class="popular-badge">Most Popular</div>
  {/if}
  
  <div class="card-header">
    <h3 class="plan-title">{title}</h3>
    <div class="price">
      <span class="amount">${price}</span>
      <span class="period">/{period}</span>
    </div>
  </div>
  
  <div class="features">
    {#each features as feature}
      <div class="feature">
        <span class="check">✓</span>
        <span>{feature}</span>
      </div>
    {/each}
  </div>
  
  <Button 
    variant={popular ? 'primary' : 'secondary'}
    onclick={onSelect}
    class="w-full"
  >
    {buttonText}
  </Button>
</div>

<!-- Usage -->
<PricingCard 
  title="Pro Plan"
  price="19.99"
  features={[
    'Unlimited mock tests',
    'Detailed analytics',
    'Study recommendations',
    'Progress tracking'
  ]}
  popular={true}
  onSelect={handleProPlanSelect}
/>
```

## Social Media Icons

### Icon Components
```svelte
<!-- social-icons/FacebookIcon.svelte -->
<script>
  let { 
    size = 24,
    color = 'currentColor',
    class: className = ''
  } = $props();
</script>

<svg 
  width={size} 
  height={size} 
  fill={color}
  class="social-icon facebook {className}"
  viewBox="0 0 24 24"
>
  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
</svg>

<!-- Usage in components -->
<div class="social-links">
  <a href="https://facebook.com/dsat16" target="_blank">
    <FacebookIcon size={20} />
  </a>
  <a href="https://instagram.com/dsat16" target="_blank">
    <InstagramIcon size={20} />
  </a>
</div>
```

## Styling System

### Design Tokens
```css
/* Tailwind config integration */
:root {
  --primary: #6366f1;
  --secondary: #8b5cf6;
  --accent: #06b6d4;
  --charcoal: #374151;
  --tangerine: #fb923c;
  --rose: #f43f5e;
  --aquamarine: #06d6a0;
  --yellow: #fbbf24;
  --purple: #8b5cf6;
}

/* Component base styles */
.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-colors;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-indigo-600 text-white hover:bg-indigo-700;
  @apply focus:ring-indigo-500;
}

.btn-secondary {
  @apply bg-gray-200 text-gray-900 hover:bg-gray-300;
  @apply focus:ring-gray-500;
}
```

### Component Styling Patterns
```svelte
<!-- Consistent styling approach -->
<script>
  let { class: className = '', variant = 'default' } = $props();
  
  // Combine base classes with variants and user classes
  let classes = $derived(`base-component variant-${variant} ${className}`);
</script>

<div class={classes}>
  <!-- Component content -->
</div>

<style>
  .base-component {
    @apply p-4 border rounded-lg;
  }
  
  .variant-primary {
    @apply bg-blue-50 border-blue-200;
  }
  
  .variant-success {
    @apply bg-green-50 border-green-200;
  }
</style>
```

## Accessibility Features

### ARIA Integration
```svelte
<!-- Accessible component patterns -->
<script>
  let { 
    label,
    describedBy,
    invalid = false,
    required = false
  } = $props();
</script>

<input
  aria-label={label}
  aria-describedby={describedBy}
  aria-invalid={invalid}
  aria-required={required}
  class="accessible-input"
  class:invalid
/>

{#if invalid && describedBy}
  <div id={describedBy} class="error-message" role="alert">
    Please fix the errors above
  </div>
{/if}
```

## Development Tips

1. **Consistent Props**: Use consistent prop naming across all components
2. **Accessibility**: Always include proper ARIA labels and keyboard navigation
3. **Responsive**: Design components to work across all screen sizes
4. **Theme Support**: Use CSS custom properties for theme-able components
5. **Performance**: Lazy load heavy components and optimize bundle size
6. **Testing**: Write component tests for critical UI interactions
7. **Documentation**: Include usage examples in component files
8. **Type Safety**: Provide proper TypeScript interfaces for all component props