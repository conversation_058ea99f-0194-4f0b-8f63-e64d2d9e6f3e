# landingPage/

Marketing website components for public-facing pages and user acquisition.

## Layout Components
- **NavBar.svelte** - Main navigation with authentication and pricing links
- **Footer.svelte** - Site footer with links and social media
- **SectionWrapper.svelte** - Consistent section styling and spacing
- **SignUpButton.svelte** - Call-to-action signup button

## Home Page Sections (/home/<USER>
- **Hero.svelte** - Main value proposition and hero section
- **Banner.svelte** - Promotional banner/announcement bar
- **SocialProof.svelte** - Testimonials, reviews, and credibility indicators  
- **CoreFeatures.svelte** - Key feature highlights and benefits
- **Comparison.svelte** - Competitive comparison table
- **HowItWorks.svelte** - Process explanation and workflow
- **Demo.svelte** - Product demonstration and preview
- **CTA.svelte** - Call-to-action section for conversions
- **Manifesto.svelte** - Brand mission and values statement

**Route Usage**: Used in (marketing) route group for landing, pricing, and info pages
**Exports**: Navbar, Footer, SectionWrapper, SignUpButton (index.ts)
**Home Exports**: All home components available via home/index.ts