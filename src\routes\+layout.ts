import posthog from 'posthog-js'
import { browser } from '$app/environment';
import type { LayoutLoad } from './$types';

export const load: LayoutLoad = ({ url, data }) => {
  const affid = url.searchParams.get('affid');

  if (browser) {
    posthog.init(
      'phc_XqrqMXQs6uFmKbnA9wPoAMe2gDKQqAXlVoOFXQBmNS3',
      { 
        api_host: 'https://lively-resonance-090c.dsat16.com',
        person_profiles: 'identified_only', // or 'always' to create profiles for anonymous users as well
      }
    )
  } 

  return { affid, role: data.role };
};
