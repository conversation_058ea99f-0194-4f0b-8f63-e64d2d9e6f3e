import { adminDB } from '$lib/server';
import type { Deck } from '$lib/types/vocab.types.ts';
import { error } from '@sveltejs/kit';
import { Timestamp } from 'firebase-admin/firestore';

export async function load({ locals, params }) {
	const { deckId } = params;

	const docRef = adminDB.collection('users').doc(locals.uid).collection('decks').doc(deckId);

	const doc = await docRef.get();

	if (!doc.exists) {
		error(404, 'Deck not found');
	}

    const docData = doc.data() as Deck;

	docData.cards.forEach((card) => {
		card.fsrsCard.due = (card.fsrsCard.due as Timestamp).toDate();
		if (card.fsrsCard.last_review) {
			card.fsrsCard.last_review = (card.fsrsCard.last_review as Timestamp).toDate();
		}
	});
	docData.createdAt = (docData.createdAt as Timestamp).toDate();
	docData.updatedAt = (docData.updatedAt as Timestamp).toDate();
	docData.id = deckId;

	// Check if there are any cards due for review
	const now = new Date();
	const hasDueCards = docData.cards.some((card) => card.fsrsCard.due <= now);

	return {
		deck: docData,
		userId: locals.uid,
		hasDueCards
	};
}