import type { Timestamp } from "firebase/firestore";
import type { Card as FSRSCard, Rating, ReviewLog, State } from "ts-fsrs";

// Vocabulary card reference data
export interface VocabCard {
    id?: number;
    word: string;
    partOfSpeech: string;
    definition: string;
    difficulty: number | null;
    charge: "Positive" | "Negative" | "Neutral";
    hint1: string | null;
    hint2: string | null;
}

/**
 * Card with FSRS fields, compatible with Firestore
 * - 'due' is Date when created client-side, Timestamp when read from Firestore
 */
export interface FSRSCardFireStore extends Omit<FSRSCard, 'due' | 'last_review'> {
    due: Date | Timestamp;
    last_review?: Date | Timestamp;
}


export interface Card {
    id: number;
    vocabCard: VocabCard;
    fsrsCard: FSRSCardFireStore;
}

interface DeckTrait {
    id: string;
    name: string;
    description: string;
    createdAt: Timestamp | Date;
    updatedAt: Timestamp | Date;
}

// Deck containing cards array
export interface Deck extends DeckTrait {
    cards: Card[];           // Array of card objects
}

// Study session data
export interface StudySession {
    id: string;
    deckId: string;
    cardsReviewed: number;
    accuracy: number;
    duration: number;
    completedAt: Timestamp;
    reviewLogs: ReviewLog[];
}

// Deck statistics for UI display
export interface DeckStats {
    total: number;
    new: number;
    learn: number;
    review: number;
    due: number;
}

// User progress tracking
export interface UserProgress {
    userId: string;
    totalCardsStudied: number;
    totalStudyTime: number; // in minutes
    streakDays: number;
    lastStudyDate: Timestamp;
    averageAccuracy: number;
    updatedAt: Timestamp;
} 