# vocabTool/

Vocabulary learning system with spaced repetition and adaptive difficulty.

## Components
- **WordCard.svelte** - Individual vocabulary card display with word, definition, and context
- **AnswerButtons.svelte** - Response buttons for spaced repetition difficulty rating (Easy, Good, Hard, Again)

**Features**:
- Spaced repetition algorithm (ts-fsrs) for optimal learning intervals
- AI-generated vocabulary explanations and context examples
- Progress tracking and retention analytics
- Deck-based organization for different vocabulary sets
- Integration with missions system for daily vocabulary goals

**Data Storage**: Firebase vocabDB for user progress and card scheduling
**AI Integration**: Google Generative AI for dynamic explanations and examples
**Routes**: Used in /study/vocab-tool/* pages for vocabulary practice sessions

**No index.ts**: Components imported directly where needed
**Integration**: Connects to missions system and dashboard progress tracking