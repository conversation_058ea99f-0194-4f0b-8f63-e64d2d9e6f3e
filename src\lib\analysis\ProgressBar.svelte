<script lang="ts">
    let { percentage } = $props();
</script>

<div class="progress-bar" style="--percentage: {percentage}%"></div>

<style>
    .progress-bar {
        width: 100%;
        height: var(--height, 0.75rem);
        background-color: var(--light-color);
        border-radius: 0.625rem;
        position: relative;
        overflow: hidden;
        outline: 1px solid var(--pitch-black);
    }


    .progress-bar::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: var(--percentage);
        height: 100%;
        background-color: var(--color);
        border-right: 1px solid var(--pitch-black);
        border-radius: 0.625rem;
    }
</style>