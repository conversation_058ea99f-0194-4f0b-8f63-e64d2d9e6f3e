<script lang="ts">
	import { onNavigate } from '$app/navigation';

	let { currentPath, role } = $props();

	// Mobile navigation state
	let isMobileMenuOpen = $state(false);

	// Toggle mobile menu
	function toggleMobileMenu() {
		isMobileMenuOpen = !isMobileMenuOpen;
		// Disable/enable body scrolling
		document.body.style.overflow = isMobileMenuOpen ? 'auto' : 'hidden';
	}

	// Close mobile menu
	function closeMobileMenu() {
		isMobileMenuOpen = false;
		// Re-enable body scrolling
		document.body.style.overflow = 'auto';
	}

	// Handle keyboard events
	function handleKeydown(event) {
		if (event.key === 'Escape' && isMobileMenuOpen) {
			closeMobileMenu();
		}
	}

	// Get current page name from path
	function getCurrentPageName(path: string) {
		if (path.includes('/dashboard')) return 'Dashboard';
		if (path.includes('/bootcamp')) return 'Bootcamp';
		if (path.includes('/question-bank')) return 'Question Bank';
		if (path.includes('/simulations')) return 'Simulations';
		if (path.includes('/vocab-tool')) return 'Vocab Tool';
		if (path.includes('/analysis')) return 'Analysis';
		return 'Study';
	}

	const navItems = [
		{
			icon: '/dashboard-icon.svg',
			label: 'Dashboard',
			link: '/study'
		},
		{
			icon: '/question-bank-icon.svg',
			label: 'Question Bank',
			link: '/study/question-bank'
		},
		{
			icon: '/simulation-icon.svg',
			label: 'Simulations',
			link: '/study/simulations'
		},
		{
			icon: '/vocab-tool-icon.svg',
			label: 'Vocab Tool',
			link: '/study/vocab-tool'
		},
		{
			icon: '/bootcamp-icon.svg',
			label: 'Bootcamp',
			link: '/bootcamp'
		}
	];

	let selectedPage = $state(navItems.findIndex((item) => item.link === currentPath));

	onNavigate(({ to }) => {
		selectedPage = navItems.findIndex((item) => item.link === to.route.id);
		closeMobileMenu();
	});
</script>

<svelte:window on:keydown={handleKeydown} />

<!-- Mobile top navbar -->
<div class="mobile-top-navbar">
	<button
		class="mobile-toggle"
		onclick={toggleMobileMenu}
		aria-label={isMobileMenuOpen ? 'Close navigation menu' : 'Open navigation menu'}
		aria-expanded={isMobileMenuOpen}
	>
		<span class="hamburger-line"></span>
		<span class="hamburger-line"></span>
		<span class="hamburger-line"></span>
	</button>
	<div class="current-page-name">
		{getCurrentPageName(currentPath)}
	</div>
</div>

<!-- Mobile backdrop -->
{#if isMobileMenuOpen}
	<div
		class="mobile-backdrop"
		onclick={closeMobileMenu}
		onkeydown={(e) => e.key === 'Enter' && closeMobileMenu()}
		role="button"
		tabindex="0"
		aria-label="Close navigation menu"
	></div>
{/if}

<nav class="navbar" class:mobile-open={isMobileMenuOpen}>
	<div class="navbar-header">
		<svg
			class="dsat16-icon-expand"
			width="102"
			height="30"
			viewBox="0 0 102 30"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M10.344 29.0061H0V1.02128H10.3303C13.1816 1.02128 15.6367 1.58152 17.6955 2.702C19.7633 3.81338 21.3575 5.41667 22.478 7.51189C23.5985 9.598 24.1587 12.094 24.1587 15C24.1587 17.9151 23.5985 20.4202 22.478 22.5155C21.3666 24.6107 19.777 26.2185 17.7091 27.339C15.6412 28.4504 13.1862 29.0061 10.344 29.0061ZM6.7639 23.2397H10.0844C11.6512 23.2397 12.9767 22.9755 14.0607 22.4471C15.1539 21.9097 15.9783 21.0397 16.534 19.8372C17.0988 18.6256 17.3812 17.0132 17.3812 15C17.3812 12.9868 17.0988 11.3835 16.534 10.1901C15.9692 8.98765 15.1357 8.12224 14.0334 7.59388C12.9402 7.05641 11.592 6.78767 9.98871 6.78767H6.7639V23.2397Z"
				fill="black"
			/>
			<path
				d="M43.2343 9.41125C43.1432 8.40919 42.7378 7.63031 42.0182 7.07463C41.3076 6.50983 40.2919 6.22743 38.971 6.22743C38.0965 6.22743 37.3677 6.3413 36.7847 6.56904C36.2017 6.79678 35.7644 7.11107 35.4729 7.51189C35.1814 7.9036 35.0311 8.35453 35.022 8.86467C35.0038 9.28371 35.0857 9.65265 35.2679 9.97149C35.4592 10.2903 35.7325 10.5727 36.0878 10.8187C36.4522 11.0555 36.8895 11.2651 37.3996 11.4473C37.9097 11.6294 38.4836 11.7889 39.1213 11.9255L41.5263 12.4721C42.9109 12.7727 44.1316 13.1735 45.1883 13.6746C46.2542 14.1756 47.1469 14.7723 47.8666 15.4646C48.5953 16.1569 49.1465 16.954 49.52 17.8559C49.8935 18.7577 50.0848 19.7689 50.0939 20.8894C50.0848 22.6567 49.6384 24.1734 48.7548 25.4396C47.8711 26.7059 46.6003 27.6761 44.9424 28.3502C43.2935 29.0243 41.3031 29.3613 38.971 29.3613C36.6298 29.3613 34.5893 29.0106 32.8493 28.3092C31.1094 27.6077 29.7566 26.5419 28.791 25.1117C27.8254 23.6815 27.3289 21.8732 27.3016 19.6869H33.7785C33.8332 20.5888 34.0746 21.3403 34.5027 21.9415C34.9309 22.5428 35.5185 22.9983 36.2654 23.308C37.0215 23.6177 37.8961 23.7726 38.889 23.7726C39.8 23.7726 40.5743 23.6496 41.212 23.4036C41.8588 23.1577 42.3552 22.8161 42.7014 22.3788C43.0476 21.9415 43.2252 21.4405 43.2343 20.8757C43.2252 20.3474 43.0612 19.8964 42.7424 19.5229C42.4236 19.1403 41.9316 18.8124 41.2666 18.5391C40.6107 18.2567 39.7727 17.9971 38.7524 17.7602L35.8282 17.077C33.405 16.5213 31.4966 15.624 30.1028 14.3851C28.709 13.1371 28.0167 11.4518 28.0258 9.32926C28.0167 7.59843 28.4813 6.08168 29.4196 4.779C30.3578 3.47632 31.656 2.4606 33.3139 1.73183C34.9719 1.00306 36.8621 0.638672 38.9847 0.638672C41.1528 0.638672 43.0339 1.00761 44.6281 1.74549C46.2314 2.47426 47.4749 3.4991 48.3585 4.81999C49.2421 6.14089 49.693 7.67131 49.7113 9.41125H43.2343Z"
				fill="black"
			/>
			<path
				d="M58.6991 29.0061H51.4296L60.8717 1.02128H69.8766L79.3187 29.0061H72.0492L65.4766 8.07213H65.258L58.6991 29.0061ZM57.7289 17.9925H72.9237V23.1304H57.7289V17.9925Z"
				fill="black"
			/>
			<path
				d="M77.9215 6.51438V1.02128H101.575V6.51438H93.0891V29.0061H86.4208V6.51438H77.9215Z"
				fill="black"
			/>
		</svg>

		<svg
			class="dsat16-icon"
			width="56"
			height="30"
			viewBox="0 0 56 30"
			fill="none"
			xmlns="http://www.w3.org/2000/svg"
		>
			<path
				d="M20.1381 0.39924V29.6008H13.0943V6.98669H12.9232L6.39282 10.9791V4.87643L13.5934 0.39924H20.1381Z"
				fill="url(#paint0_linear_4598_2846)"
			/>
			<path
				d="M38.2999 30C36.7029 30 35.1725 29.7433 33.7086 29.23C32.2448 28.7072 30.9425 27.8802 29.8018 26.749C28.6611 25.6084 27.7628 24.116 27.1069 22.2719C26.451 20.4183 26.1278 18.1606 26.1374 15.499C26.1469 13.0941 26.4415 10.9363 27.0214 9.02567C27.6012 7.10551 28.4282 5.47529 29.5024 4.13498C30.586 2.79468 31.8788 1.77281 33.3807 1.06939C34.8921 0.356464 36.5794 0 38.4425 0C40.4862 0 42.2875 0.39924 43.8465 1.19772C45.4149 1.98669 46.6697 3.05133 47.6107 4.39163C48.5518 5.72243 49.1079 7.20532 49.279 8.8403H42.3351C42.1259 7.91825 41.6649 7.21958 40.952 6.7443C40.2486 6.2595 39.4121 6.01711 38.4425 6.01711C36.6554 6.01711 35.3199 6.79182 34.4358 8.34125C33.5613 9.89068 33.1145 11.9629 33.0955 14.558H33.2809C33.6801 13.6835 34.2552 12.9325 35.0062 12.3051C35.7571 11.6778 36.6174 11.1977 37.587 10.865C38.5661 10.5228 39.6022 10.3517 40.6953 10.3517C42.4444 10.3517 43.9891 10.7557 45.3294 11.5637C46.6697 12.3717 47.72 13.4791 48.4805 14.8859C49.241 16.2833 49.6164 17.885 49.6069 19.6911C49.6164 21.7253 49.1412 23.5219 48.1811 25.0808C47.221 26.6302 45.8902 27.8375 44.1887 28.7025C42.4967 29.5675 40.5337 30 38.2999 30ZM38.2571 24.5817C39.1221 24.5817 39.8969 24.3774 40.5813 23.9686C41.2657 23.5599 41.8027 23.0038 42.1925 22.3004C42.5822 21.597 42.7723 20.8032 42.7628 19.9192C42.7723 19.0257 42.5822 18.2319 42.1925 17.538C41.8123 16.8441 41.2799 16.2928 40.5955 15.884C39.9206 15.4753 39.1459 15.2709 38.2714 15.2709C37.6345 15.2709 37.0404 15.3897 36.4891 15.6274C35.9377 15.865 35.4577 16.1977 35.0489 16.6255C34.6497 17.0437 34.336 17.538 34.1079 18.1084C33.8797 18.6692 33.7609 19.2776 33.7514 19.9335C33.7609 20.7985 33.9605 21.5827 34.3503 22.2861C34.74 22.9895 35.2723 23.5504 35.9472 23.9686C36.6221 24.3774 37.3921 24.5817 38.2571 24.5817Z"
				fill="url(#paint1_linear_4598_2846)"
			/>
			<defs>
				<linearGradient
					id="paint0_linear_4598_2846"
					x1="51.6609"
					y1="-5.86693"
					x2="18.3696"
					y2="-10.7627"
					gradientUnits="userSpaceOnUse"
				>
					<stop stop-color="#66E2FF" />
					<stop offset="1" stop-color="#FF66C4" />
				</linearGradient>
				<linearGradient
					id="paint1_linear_4598_2846"
					x1="51.6609"
					y1="-5.86693"
					x2="18.3696"
					y2="-10.7627"
					gradientUnits="userSpaceOnUse"
				>
					<stop stop-color="#66E2FF" />
					<stop offset="1" stop-color="#FF66C4" />
				</linearGradient>
			</defs>
		</svg>
	</div>

	<ul class="nav-items">
		{#each navItems as item, index}
			{#if item.link !== '/bootcamp' || role === 'Pro'}
				<li>
					<a href={item.link}>
						<button class="nav-link" class:active={selectedPage === index}>
							<span class="icon">
								<img src={item.icon} alt={item.label} width="24" height="24" />
							</span>
							<span class="nav-link-text">{item.label}</span>
						</button>
					</a>
				</li>
			{/if}
		{/each}
	</ul>
</nav>

<style>
	.navbar {
		position: fixed;
		left: 0;
		top: 0;
		height: max(100vh, 100%);
		background-color: var(--white);
		border-right: 2px solid var(--pitch-black);
		padding: 6px 10px;
		width: 4.75rem;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 2rem;
		transition: width 0.3s ease;
		overflow: hidden;
		z-index: 1000;
	}

	.navbar:hover {
		width: 15rem;
	}

	.navbar-header {
		margin-top: 30px;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.nav-items {
		list-style: none;
		padding: 0;
		margin-top: 3rem;
		display: flex;
		flex-direction: column;
		gap: 1rem;
		cursor: pointer;
		width: 100%;
	}

	.nav-link {
		display: flex;
		align-items: center;
		padding: 0.75rem;
		text-decoration: none;
		color: var(--charcoal);
		border-radius: 1rem;
		transition: background-color 0.2s ease;
		background-color: var(--white);
		width: 100%;
		height: 56px;
		border: none;
		cursor: pointer;
		gap: 0.75rem;
	}

	.nav-link:hover {
		background-color: var(--light-sky-blue);
	}

	.nav-link-text {
		color: #000;
		font-family: Inter;
		font-size: 20px;
		font-style: normal;
		font-weight: 600;
		line-height: normal;
		white-space: nowrap;
		opacity: 0;
		transform: translateX(-10px);
		transition:
			opacity 0.3s ease,
			transform 0.3s ease;
	}

	.dsat16-icon {
		transform: translateX(-50px);
		transition: transform 0.3s ease;
	}

	.dsat16-icon-expand {
		opacity: 0;
		transform: translateX(-50px);
		transition:
			opacity 0.3s ease,
			transform 0.3s ease;
	}

	.navbar:hover .nav-link-text,
	.navbar:hover .dsat16-icon-expand,
	.navbar:hover .dsat16-icon {
		opacity: 1;
		transform: translateX(0);
	}

	.nav-link.active {
		background-color: var(--sky-blue);
		border: 1.5px solid black;
		border-radius: 1rem;
		white-space: nowrap;
		cursor: pointer;
		box-shadow: 2px 2px black;
	}

	.icon {
		display: flex;
		justify-content: center;
		align-items: center;
		min-width: 24px;
	}

	/* Mobile top navbar - hidden by default */
	.mobile-top-navbar {
		display: none;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 4rem;
		background: var(--white);
		border-bottom: 1px solid var(--pitch-black);
		z-index: 2;
		align-items: center;
		padding: 0 1rem;
		gap: 1rem;
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}

	.mobile-toggle {
		background: var(--white);
		border: 1px solid var(--pitch-black);
		border-radius: 0.5rem;
		padding: 0.5rem;
		box-shadow: 0.25rem 0.25rem 0 var(--pitch-black);
		cursor: pointer;
		display: flex;
		flex-direction: column;
		gap: 0.25rem;
		width: 2.5rem;
		height: 2.5rem;
		justify-content: center;
		align-items: center;
	}

	.mobile-toggle:active {
		box-shadow: none;
		transform: translate(0.25rem, 0.25rem);
	}

	.hamburger-line {
		width: 1.25rem;
		height: 0.125rem;
		background-color: var(--pitch-black);
		transition: all 0.3s ease;
	}

	.current-page-name {
		font-family: 'Inter';
		font-size: 1.25rem;
		font-weight: 600;
		color: var(--pitch-black);
	}

	/* Mobile backdrop */
	.mobile-backdrop {
		display: none;
		position: fixed;
		top: 0;
		left: 0;
		width: 100vw;
		height: 100vh;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 999;
		cursor: pointer;
	}

	/* Mobile responsive styles */
	@media (max-width: 1024px) {
		.mobile-top-navbar {
			display: flex;
		}

		.mobile-backdrop {
			display: block;
		}

		.navbar {
			transform: translateX(-100%);
			transition: transform 0.3s ease;
			z-index: 1000;
		}

		.navbar.mobile-open {
			transform: translateX(0);
		}

		.navbar:hover {
			width: 4.75rem; /* Disable hover expansion on mobile */
		}

		.navbar:hover .nav-link-text,
		.navbar:hover .dsat16-icon-expand,
		.navbar:hover .dsat16-icon {
			opacity: 0;
			transform: translateX(-50px);
		}

		.navbar.mobile-open .nav-link-text,
		.navbar.mobile-open .dsat16-icon-expand,
		.navbar.mobile-open .dsat16-icon {
			opacity: 1;
			transform: translateX(0);
		}

		.navbar.mobile-open {
			width: 15rem;
		}
	}
</style>
