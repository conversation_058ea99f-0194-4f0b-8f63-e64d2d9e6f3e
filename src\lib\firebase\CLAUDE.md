# firebase/

Firebase integration layer managing authentication, database, and vocabulary storage.

## Core Services
- **config.ts** - Firebase app configuration (hardcoded client config, consider env vars for production)
- **auth.svelte.ts** - Authentication state management using Svelte 5 runes ($state, $derived, $effect)
- **firestore.ts** - Firestore database client and utilities
- **vocabDB.ts** - Specialized vocabulary data management with spaced repetition (ts-fsrs)

## State Management
- **index.ts** - Central exports: app, auth, user, db, and vocab utilities

## Key Features
- **Authentication**: Firebase Auth with session cookies for server-side verification
- **User Management**: Real-time role updates ("Pro", "Bootcamp", "Pro+Bootcamp") via custom claims
- **Data Persistence**: User progress, test results, and vocab learning data
- **Real-time Updates**: Firestore listeners for live data sync

**Region**: asia-southeast1 deployment
**Integration**: Works with server-side auth in hooks.server.ts for role-based access control

**Exports**: app, auth, user, db, vocabulary functions