# Analysis System

Post-test analysis and visualization components that provide detailed performance insights and personalized study recommendations.

## Overview

The analysis system processes test results to generate comprehensive performance reports, score predictions, and AI-powered study plans. It integrates with Firebase for data persistence and provides shareable analysis links.

## Architecture

```
analysis/
├── index.ts                      # Exports main Analysis component
├── Analysis.svelte               # Main container orchestrating all components
├── Overview.svelte               # Test summary and key metrics
├── EstimatedScore.svelte         # SAT score prediction display
├── ScorePerModule.svelte         # Vertical module breakdown
├── HorizontalScorePerModule.svelte # Horizontal module layout
├── SkillAssessmentChart.svelte   # Detailed skill analysis
├── SkillSummary.svelte          # Condensed skill overview  
├── StudyPlan.svelte             # AI-generated recommendations
├── StudyPlanCard.svelte         # Individual study items
├── QuestionTypeProgess.svelte   # Progress by question type
├── ScoreChange.svelte           # Score improvement tracking
├── CircularProgressBar.svelte   # Circular progress indicators
├── ProgressBar.svelte           # Linear progress bars
├── SharePopUp.svelte            # Sharing modal
├── ShareLink.svelte             # Link generation
└── SendToMail.svelte            # Email sharing
```

## Core Components

### Main Analysis Container
```svelte
<!-- Analysis.svelte - Main orchestrator -->
<script>
  let { data } = $props(); // Test results data
  
  let skills = $derived([
    { sectionTitle: 'Reading', isVerbal: true, sections: data.scoreOfTypes.slice(0, 4) },
    { sectionTitle: 'Writing', isVerbal: true, sections: data.scoreOfTypes.slice(4, 8) },
    { sectionTitle: 'Math', isVerbal: false, sections: data.scoreOfTypes.slice(8) }
  ]);
</script>
```

### Score Visualization
```svelte
<!-- EstimatedScore.svelte -->
<EstimatedScore 
  totalScore={data.totalScore}
  mathScore={data.mathScore} 
  verbalScore={data.verbalScore}
  confidence={data.confidence}
/>

<!-- ScorePerModule.svelte -->
<ScorePerModule modules={data.moduleScores} />
```

### Progress Tracking
```svelte
<!-- QuestionTypeProgess.svelte -->
<QuestionTypeProgess 
  correct={data.correctByType}
  total={data.totalByType}
/>

<!-- ScoreChange.svelte -->
<ScoreChange 
  previousScore={data.previousScore}
  currentScore={data.currentScore}
  improvement={data.improvement}
/>
```

## Data Structure

### Test Results Data
```typescript
interface AnalysisData {
  totalScore: number;           // Overall SAT score (400-1600)
  mathScore: number;           // Math section score (200-800)
  verbalScore: number;         // Verbal section score (200-800)
  confidence: number;          // Confidence interval (0-100)
  scoreOfTypes: Array<{        // Performance by question type
    type: string;
    correct: number;
    total: number;
    percentage: number;
  }>;
  moduleScores: Array<{        // Module-level breakdown
    name: string;
    score: number;
    maxScore: number;
  }>;
  studyPlan?: {               // AI-generated recommendations
    recommendations: Array<{
      skill: string;
      priority: 'high' | 'medium' | 'low';
      timeEstimate: string;
      resources: string[];
    }>;
  };
}
```

## Usage Examples

### Basic Analysis Display
```svelte
<script>
  import { Analysis } from '$lib/analysis';
  
  let testResults = {
    totalScore: 1420,
    mathScore: 720,
    verbalScore: 700,
    // ... rest of data
  };
</script>

<Analysis data={testResults} />
```

### Individual Components
```svelte
<script>
  import { 
    EstimatedScore, 
    SkillAssessmentChart, 
    StudyPlan 
  } from '$lib/analysis';
</script>

<EstimatedScore {totalScore} {confidence} />
<SkillAssessmentChart skills={skillData} />
<StudyPlan recommendations={aiRecommendations} />
```

## Sharing Features

### Share Analysis Results
```svelte
<!-- SharePopUp.svelte -->
<script>
  import { SharePopUp, ShareLink, SendToMail } from '$lib/analysis';
  
  let analysisId = 'analysis_123';
  let shareUrl = `https://example.com/share-analysis/${analysisId}`;
</script>

<SharePopUp>
  <ShareLink url={shareUrl} />
  <SendToMail {analysisId} email="<EMAIL>" />
</SharePopUp>
```

## Integration Points

### With Mock Test System
```typescript
// After test completion in mockTest
import { Analysis } from '$lib/analysis';

const analysisData = processTestResults(testSession);
// Navigate to analysis page with results
goto('/study/analysis/' + analysisData.id);
```

### With Firebase Storage
```typescript
// Store analysis results
import { db } from '$lib/firebase';
import { doc, setDoc } from 'firebase/firestore';

await setDoc(doc(db, 'analyses', analysisId), {
  userId: user.uid,
  testData: analysisData,
  createdAt: new Date(),
  shared: false
});
```

### With Study Plan Generation
```typescript
// AI-powered study recommendations
import { geminiAI } from '$lib/server';

const studyPlan = await generateStudyPlan(analysisData);
analysisData.studyPlan = studyPlan;
```

## Styling & Layout

- Uses Tailwind CSS with custom design system
- Responsive layouts for mobile and desktop
- Interactive charts and progress indicators
- Print-friendly styling for PDF export
- Consistent color coding for score ranges

## Performance Considerations

- Lazy loading of chart components
- Efficient data processing with derived stores
- Optimized image generation for sharing
- Minimal DOM manipulation for smooth animations

## Development Tips

1. **Adding New Metrics**: Extend the `AnalysisData` interface in types
2. **Custom Visualizations**: Create new chart components following existing patterns
3. **Sharing**: Update share URL generation for new analysis features
4. **Testing**: Mock test result data for component development
5. **Accessibility**: Ensure charts have proper ARIA labels and descriptions