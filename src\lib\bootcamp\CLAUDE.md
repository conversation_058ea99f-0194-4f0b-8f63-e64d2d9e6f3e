# bootcamp/

Bootcamp content management and navigation system for structured SAT preparation courses.

## Components
- **NavContent.svelte** - Navigation component for bootcamp sections and lessons
- **Note.svelte** - Basic note/content display component  
- **NoteHyper.svelte** - Enhanced note component with hyperlink support

## Content Management
- **allAdditionalResources.js** - Registry of supplementary materials and resources
- **allLectures.js** - Registry of video lectures and lesson content
- **notesData/** - Individual note files (0.js through 10.js) containing lesson notes and materials

## Data Structure
**index.js** - Main module exports for Note and NavContent components

**Content Source**: Integrates with Contentful CMS for dynamic content management
**Access Control**: Requires "Bootcamp" or "Pro" user role for access
**Routes**: Used in /bootcamp/* routes for lectures, notes, and additional resources

**Exports**: Note, NavContent components