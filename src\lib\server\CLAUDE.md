# server/

Backend service integrations and server-side utilities for external APIs and databases.

## Core Services
- **admin.ts** - Firebase Admin SDK for server-side authentication and Firestore access
- **supabase.ts** - Supabase client for question bank and structured content
- **contentful.ts** - Contentful CMS client for bootcamp lectures and study materials  
- **redis.ts** - Upstash Redis client for caching and rate limiting
- **ai.ts** - Google Generative AI (Gemini) integration for vocabulary features and content generation

## Central Exports
- **index.ts** - Re-exports all services: client, adminDB, adminAuth, supabase, redis, geminiAI

## Usage Context
- **Authentication**: Firebase Admin for server-side user verification and role management
- **Content Management**: Contentful for dynamic bootcamp content, Supabase for question data
- **Caching**: Redis for performance optimization and API rate limiting
- **AI Features**: Gemini AI for study plan generation and vocabulary explanations
- **Region**: Services configured for asia-southeast1 deployment

**Import Pattern**: Used in API routes and server-side code via centralized exports