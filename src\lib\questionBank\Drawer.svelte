<script>
    let isOpen = $state(false);

    function toggleDrawer() {
        isOpen = !isOpen;
    }

    let { children } = $props();
</script>


<!-- Overlay -->
{#if isOpen}
    <div
        class="overlay fixed top-0 left-0 right-0 bottom-0 z-40 bg-black/50 "
        onclick={toggleDrawer}
        onkeydown={(e) => e.key === 'Escape' && toggleDrawer()}
        role="button"
        tabindex="0"
        aria-label="Close drawer"
    ></div>
{/if}

<!-- Progress Tracker Drawer -->
<div class="drawer bg-white h-[50vh] fixed left-0 right-0 bottom-0 z-50 flex flex-col items-center p-3 px-40 translate-y-[calc(100%-35px)] transition-transform duration-500 ease-[cubic-bezier(0.32,0.72,0,1)] border-[1px] border-solid border-black rounded-t-[12px]" class:translate-y-0={isOpen}>
    <button class="handle bg-[#66e2ff] h-[8px] w-[100px] rounded-[13px]" aria-label="Open/close drawer" onclick={toggleDrawer}></button>
    <div class="drawer-content flex flex-col items-center mt-12 gap-12 max-h-[80vh] w-full h-full">
        {@render children?.()}
    </div>
</div>