# ui/

Reusable UI component library with consistent styling and behavior across the application.

## Core Components
- **Button.svelte** - Primary action buttons with loading states
- **LoadingButton.svelte** - Button with built-in loading spinner and disabled state
- **Input.svelte** - Styled text input with validation support
- **Checkbox.svelte** - Custom checkbox component
- **Dropdown.svelte** - Select dropdown with custom styling
- **Slider.svelte** - Range slider input component
- **PopUp.svelte** - Modal dialog component
- **HelpIcon.svelte** - Tooltip help icon for UI guidance

## Specialized Components
- **PracticeQuestion.svelte** - Reusable question display for practice modes
- **QuestionHeader.svelte** - Question metadata display (difficulty, type, etc.)
- **PricingTimeline.svelte** - Subscription plan timeline visualization
- **WaitlistForm.svelte** - Email capture form for feature waitlists
- **MarkBarUI.svelte** - Question navigation bar component

## Sub-modules
- **typography/** - Text components (H1-H5, P1-P3) with wrappers for consistent styling
- **pricing-cards/** - Subscription plan cards (Minitest, Sprint, Commitment)
- **social-icons/** - Social media icon components (Facebook, Instagram, Threads, YouTube)

## Exports
**index.ts** - Main UI components and typography exports
**Sub-module exports** - Organized by feature area for clean imports

**Usage**: `import { Button, H1, MinitestCard } from '$lib/ui'`
**Styling**: Tailwind CSS with custom design system variables