# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Start development server:**
```bash
npm run dev
```

**Build for production:**
```bash
npm run build
```

**Type checking:**
```bash
npm run check
```

**Linting and formatting:**
```bash
npm run lint
npm run format
```

**Preview production build:**
```bash
npm run preview
```

## Project Architecture

### Tech Stack
- **Frontend**: SvelteKit 5 with TypeScript, Tailwind CSS
- **Backend Services**: Firebase (Auth, Firestore), Supabase, Stripe, Contentful CMS
- **AI Integration**: Google Generative AI for vocabulary features
- **Analytics**: PostHog for user tracking and analytics
- **Deployment**: Firebase hosting with serverless backend (asia-southeast1)

### Route Structure
The application uses SvelteKit's file-based routing with several key route groups:

- **`(marketing)/`**: Public marketing pages (landing, simulation info, pricing)
- **`study/`**: Protected study dashboard and tools (requires Pro role)
- **`bootcamp/`**: Bootcamp content (requires Bootcamp or Pro role)
- **`api/`**: Server-side API endpoints
- **`share-analysis/`**: Public analysis sharing functionality

### Authentication & Authorization
- Firebase Authentication handles user login/signup
- Custom claims system manages user roles: "Pro", "Bootcamp", "Pro+Bootcamp"
- Server-side authentication implemented in `src/hooks.server.ts`
- Role-based access control enforced at the route level
- Session cookies used for server-side auth verification

### Key Architecture Patterns

**State Management:**
- Svelte 5 runes for reactive state (`$state`, `$derived`, `$effect`)
- Firebase auth state managed in `src/lib/firebase/auth.svelte.ts`
- User data persisted in Firestore with real-time listeners for role updates

**Database Architecture:**
- Firebase Firestore for user data, test results, and analytics
- Supabase for question banks and structured content
- Contentful CMS for bootcamp lectures and study materials
- Redis (Upstash) for caching and rate limiting

**Content Management:**
- Questions and test data stored in Supabase
- Bootcamp content (lectures, notes, resources) managed via Contentful
- Static assets served from Firebase hosting

### Key Components & Features

**Mock Test System:**
- Adaptive testing simulation with real-time scoring
- Question types: Multiple choice, math (with calculator), verbal reasoning
- Timer management and section-based testing
- Detailed performance analysis and score prediction

**Study Tools:**
- Vocabulary learning with spaced repetition (ts-fsrs)
- Question bank practice with progress tracking
- Performance analytics and study plan generation

**Subscription System:**
- Stripe integration for payment processing
- Webhook handling for subscription events
- Role-based feature access control

## Important File Locations

- **Firebase Config**: `src/lib/firebase/config.ts` (hardcoded client config)
- **Server Services**: `src/lib/server/` (admin, contentful, supabase, redis, AI)
- **Type Definitions**: `src/lib/types/` (question types, vocab types, database types)
- **UI Components**: `src/lib/ui/` (reusable components)
- **Feature Modules**: `src/lib/analysis/`, `src/lib/mockTest/`, `src/lib/vocabTool/`

## Environment & Configuration

- Firebase project configured for asia-southeast1 region
- Environment variables handled via SvelteKit's standard approach
- Client-side Firebase config is hardcoded (consider env vars for production)
- PostHog analytics integrated for user tracking and error monitoring

## Development Guidelines

- Follow existing Svelte 5 patterns with runes
- Follow neo-brutalism UI patterns established
- Maintain TypeScript strict mode compliance
- Use Tailwind for styling with custom color variables
- Implement proper error handling with PostHog error tracking
- Follow role-based access patterns for new features
- Test authentication flows with different user roles