<script>
    import { H3 } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    
    let { 
        headline = "Don't just take our word for it",
        logoCount = 10
    } = $props();
</script>

<SectionWrapper --bg-color="var(--sky-blue)" --padding-top="4rem" --padding-bottom="4rem">
<div class="social-proof">
    <H3 --text-color="white">{headline}</H3>
    <div class="logos-container">
        <div class="logos-track">
            {#each Array(logoCount) as _, i}
                <div class="logo-item">
                    <div class="logo-placeholder">LOGO {i + 1}</div>
                </div>
            {/each}
            <!-- Duplicate logos for seamless loop -->
            {#each Array(logoCount) as _, i}
                <div class="logo-item">
                    <div class="logo-placeholder">LOGO {i + 1}</div>
                </div>
            {/each}
        </div>
    </div>
</div>
</SectionWrapper>

<style>
    /* Social Proof */
    .social-proof {
        text-align: center;
                width: 100%;
    }
    
    .logos-container {
        overflow: hidden;
        margin-top: 3rem;
        width: 100%;
        position: relative;
        /* Create gradient mask for smooth fade-out edges */
        mask: linear-gradient(
            to right,
            transparent 0%,
            black 10%,
            black 90%,
            transparent 100%
        );
        -webkit-mask: linear-gradient(
            to right,
            transparent 0%,
            black 10%,
            black 90%,
            transparent 100%
        );
    }

    /* Fallback for browsers that don't support mask */
    .logos-container::before,
    .logos-container::after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 5rem;
        z-index: 2;
        pointer-events: none;
    }

    .logos-container::before {
        left: 0;
        background: linear-gradient(
            to right,
            var(--sky-blue) 0%,
            transparent 100%
        );
    }

    .logos-container::after {
        right: 0;
        background: linear-gradient(
            to left,
            var(--sky-blue) 0%,
            transparent 100%
        );
    }

    .logos-track {
        display: flex;
        gap: 2rem;
        animation: scroll-left 30s linear infinite;
        width: fit-content;
    }

    @keyframes scroll-left {
        0% {
            transform: translateX(0);
        }
        100% {
            transform: translateX(-50%);
        }
    }

    .logo-item {
        flex-shrink: 0;
        display: flex;
        justify-content: center;
        min-width: 12.5rem;
    }

    .logo-placeholder {
        background: white;
        color: var(--pitch-black);
        padding: 2rem 1rem;
        border: 0.25rem solid var(--pitch-black);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        min-height: 5rem;
        width: 100%;
    }

    .logo-placeholder:hover {
        transform: translate(-0.25rem, -0.25rem);
        box-shadow: 0.5rem 0.5rem 0 var(--pitch-black);
    }
    
    /* Mobile Responsiveness */
    @media (max-width: 48rem) {
        .logos-container {
            /* Adjust gradient mask for mobile - slightly more fade area */
            mask: linear-gradient(
                to right,
                transparent 0%,
                black 15%,
                black 85%,
                transparent 100%
            );
            -webkit-mask: linear-gradient(
                to right,
                transparent 0%,
                black 15%,
                black 85%,
                transparent 100%
            );
        }

        .logos-container::before,
        .logos-container::after {
            width: 3.75rem; /* Smaller fade width on mobile */
        }

        .logos-track {
            gap: 1rem;
            animation-duration: 20s; /* Faster on mobile */
        }

        .logo-item {
            min-width: 9.375rem;
        }

        .logo-placeholder {
            padding: 1.5rem 0.5rem;
            min-height: 3.75rem;
        }
    }
</style>
