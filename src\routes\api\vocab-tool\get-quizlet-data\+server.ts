import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import puppeteer from "puppeteer-extra";
import StealthPlugin from "puppeteer-extra-plugin-stealth";
import { redis } from '$lib/server';
import { Ratelimit } from '@upstash/ratelimit';

const rateLimit = new Ratelimit({
    redis,
    limiter: Ratelimit.slidingWindow(3, "1 h"),
})


export const POST: RequestHandler = async ({ request }) => {
    try {
        const { uid, url } = await request.json();

        const { success } = await rateLimit.limit(uid + ":getQuizletData");

        if (!success) {
            return json({
                success: false,
                message: 'Exceeded rate limit. Please try again in an hour.'
            });
        }

        if (!url) {
            return json({
                success: false,
                message: 'URL is required'
            });
        }

        // Extract deck ID from Quizlet URL
        const deckId = extractDeckId(url.trim());
        if (!deckId) {
            return json({
                success: false,
                message: 'Invalid Quizlet URL. Please provide a valid Quizlet set URL.'
            });
        }

        const data = await scrapeQuizletData(deckId);
        return json({ success: true, data });

    } catch (error) {
        console.error('Error fetching Quizlet data:', error);
        return json({
            success: false,
            message: 'An error occurred while fetching data from Quizlet. Please try again.'
        });
    }
};


async function scrapeQuizletData(deckId: string) {

    puppeteer.use(StealthPlugin());
    const browser = await puppeteer.launch({ headless: true });
    const page = await browser.newPage();

    const quizletApiUrl = `https://quizlet.com/webapi/3.9/studiable-item-documents?filters[studiableContainerId]=${deckId}&filters[studiableContainerType]=1&perPage=1000&page=1`;

    await page.goto(quizletApiUrl);

    const data = await page.evaluate(() => {
        return JSON.parse(document.querySelector('pre').textContent);
    });

    const formattedData = extractQuizletData(data);

    await browser.close();
    return formattedData;
}

function extractQuizletData(jsonData) {
    const extractedData = [];
    
    // Check if the data has the expected structure
    if (!jsonData.responses || !jsonData.responses[0] || !jsonData.responses[0].models || !jsonData.responses[0].models.studiableItem) {
        console.log('Invalid data structure');
        return [];
    }
    
    const studiableItems = jsonData.responses[0].models.studiableItem;
    
    studiableItems.forEach(item => {
        if (item.cardSides && item.cardSides.length >= 2) {
            const wordSide = item.cardSides.find(side => side.label === 'word');
            const definitionSide = item.cardSides.find(side => side.label === 'definition');
            
            if (wordSide && definitionSide) {
                // Extract word text
                const wordText = wordSide.media && wordSide.media[0] && wordSide.media[0].plainText 
                ? wordSide.media[0].plainText.trim() 
                : '';
                
                // Extract definition text (only text media, ignore images)
                let definitionText = '';
                if (definitionSide.media) {
                    const textMedia = definitionSide.media.find(media => media.type === 1);
                    if (textMedia && textMedia.plainText) {
                        definitionText = textMedia.plainText.trim();
                    }
                }
                
                // Only add if both word and definition exist
                if (wordText && definitionText) {
                    extractedData.push({
                        word: wordText,
                        definition: definitionText
                    });
                }
            }
        }
    });
    
    return extractedData;
}


// Helper function to extract deck ID from Quizlet URL
function extractDeckId(url: string): string | null {
    try {
        // Handle various Quizlet URL formats:
        // https://quizlet.com/48687430/vocabulary-flash-cards/
        // https://quizlet.com/48687430/vocabulary-flash-cards/?funnelUUID=...
        // https://quizlet.com/48687430

        const urlObj = new URL(url);
        const pathname = urlObj.pathname;

        // Extract the deck ID (first number after the domain)
        const match = pathname.match(/\/(\d+)/);
        return match ? match[1] : null;
    } catch (error) {
        console.error('Error parsing URL:', error);
        return null;
    }
}