# analysis/

Test result analysis and visualization components for post-simulation reporting.

## Key Components
- **Analysis.svelte** - Main analysis container, orchestrates all visualization components
- **Overview.svelte** - Test summary with overall performance metrics
- **EstimatedScore.svelte** - SAT score prediction display with confidence intervals
- **ScorePerModule.svelte** - Module-by-module score breakdown (Math, Verbal, etc.)
- **HorizontalScorePerModule.svelte** - Horizontal layout alternative for score breakdown
- **SkillAssessmentChart.svelte** - Detailed skill analysis with strengths/weaknesses
- **StudyPlan.svelte** - AI-generated personalized study recommendations
- **StudyPlanCard.svelte** - Individual study plan item cards
- **QuestionTypeProgess.svelte** - Progress tracking by question type
- **ScoreChange.svelte** - Score improvement tracking over time
- **SkillSummary.svelte** - Condensed skill performance overview

## Progress Indicators
- **CircularProgressBar.svelte** - Circular progress indicator for completion rates
- **ProgressBar.svelte** - Linear progress bars for metrics

## Sharing Features
- **SharePopUp.svelte** - Modal for sharing analysis results
- **ShareLink.svelte** - Shareable link generation component
- **SendToMail.svelte** - Email sharing functionality

**Exports**: Only Analysis component (main entry point)
**Data Sources**: Firestore test results, AI-generated insights