# Type System

Comprehensive TypeScript type definitions ensuring type safety across the entire application, from database schemas to component interfaces.

## Overview

The types system provides complete TypeScript coverage for all data structures, API interfaces, and component props. It includes auto-generated database schemas from Supabase and carefully crafted domain-specific types for questions, vocabulary, missions, and user data.

## Architecture

```
types/
├── index.ts              # Central type exports
├── question.types.ts     # Question and test-related types
├── vocab.types.ts        # Vocabulary learning types  
├── mission.types.ts      # Gamification system types
└── supabase.types.ts     # Auto-generated database schema
```

## Core Type Categories

### Question & Test Types
```typescript
// question.types.ts - Test and question structures

// Basic question structure
export interface Question {
  id: string;
  question_text: string;
  question_type: QuestionType;
  domain: Domain;
  skill: string;
  difficulty: Difficulty;
  choices: string[];
  correct_answer: string;
  explanation: string;
  passage_text?: string;
  passage_title?: string;
  graph_data?: GraphData;
  created_at: string;
  updated_at: string;
}

// Question classification
export type QuestionType = 
  | 'Reading and Writing'
  | 'Math';

export type Domain = 
  | 'Algebra'
  | 'Advanced Math'
  | 'Geometry and Trigonometry'
  | 'Problem-Solving and Data Analysis'
  | 'Reading Comprehension'
  | 'Grammar and Usage'
  | 'Rhetoric and Synthesis';

export type Difficulty = 1 | 2 | 3 | 4 | 5;

// Test structure
export interface TestData {
  id: string;
  name: string;
  type: TestType;
  sections: TestSection[];
  timeLimit: number; // Total minutes
  sectionTimeLimits: number[];
  instructions?: string;
  created_at: string;
}

export type TestType = 'full' | 'minitest' | 'practice';

export interface TestSection {
  id: string;
  name: string;
  questions: Question[];
  timeLimit: number; // Minutes
  instructions?: string;
  canUseCalculator?: boolean;
  hasReferenceSheet?: boolean;
}

// User responses and results
export interface TestResult {
  id: string;
  userId: string;
  testId: string;
  answers: Record<string, string>; // questionId -> answer
  timeSpent: number; // Total seconds
  sectionTimes: number[]; // Seconds per section
  totalScore: number;
  mathScore: number;
  verbalScore: number;
  moduleScores: ModuleScore[];
  completed_at: string;
}

export interface ModuleScore {
  module: string;
  rawScore: number;
  scaledScore: number;
  percentile: number;
}
```

### Vocabulary Learning Types
```typescript
// vocab.types.ts - Spaced repetition and vocabulary learning

// Core vocabulary card
export interface VocabCard {
  id: string;
  word: string;
  definition: string;
  example: string;
  difficulty: VocabDifficulty;
  frequency: number; // How common in SAT
  partOfSpeech: PartOfSpeech;
  
  // Spaced repetition data (ts-fsrs format)
  due: Date;
  stability: number;
  difficulty_sr: number; // Separate from word difficulty
  elapsed_days: number;
  scheduled_days: number;
  reps: number;
  lapses: number;
  state: CardState;
  last_review: Date;
}

export type VocabDifficulty = 'easy' | 'medium' | 'hard';
export type PartOfSpeech = 'noun' | 'verb' | 'adjective' | 'adverb' | 'other';

export type CardState = 'new' | 'learning' | 'review' | 'relearning';

// Vocabulary decks
export interface VocabDeck {
  id: string;
  name: string;
  description: string;
  cards: VocabCard[];
  category: VocabCategory;
  difficulty: VocabDifficulty;
  estimatedTime: number; // Minutes to complete
  created_at: string;
}

export type VocabCategory = 
  | 'high_frequency'
  | 'academic'
  | 'literary'
  | 'scientific'
  | 'historical'
  | 'custom';

// User progress
export interface VocabProgress {
  userId: string;
  deckId: string;
  cardsLearned: number;
  cardsReviewed: number;
  totalCards: number;
  accuracy: number;
  averageTime: number; // Seconds per card
  streakDays: number;
  lastStudied: Date;
  nextReview: Date;
  mastery: Record<string, CardMastery>; // cardId -> mastery
}

export interface CardMastery {
  cardId: string;
  masteryLevel: MasteryLevel;
  timesCorrect: number;
  timesIncorrect: number;
  averageResponseTime: number;
  lastSeen: Date;
}

export type MasteryLevel = 'learning' | 'familiar' | 'mastered' | 'overlearned';

// Review session
export interface ReviewSession {
  id: string;
  userId: string;
  deckId: string;
  cardsReviewed: VocabCard[];
  responses: ReviewResponse[];
  startTime: Date;
  endTime: Date;
  accuracy: number;
}

export interface ReviewResponse {
  cardId: string;
  rating: ReviewRating;
  responseTime: number; // Milliseconds
  timestamp: Date;
}

export type ReviewRating = 'again' | 'hard' | 'good' | 'easy';
```

### Mission & Gamification Types
```typescript
// mission.types.ts - Gamification and progress tracking

// Mission definitions
export interface Mission {
  id: string;
  name: string;
  description: string;
  metric: MissionMetric;
  target: number;
  period: MissionPeriod;
  reward: number; // Points
  route?: string; // Where to go to complete
  buttonText?: string;
  icon?: string;
}

export enum MissionMetric {
  QUESTIONS_ANSWERED = 'questions_answered',
  VOCAB_SESSION = 'vocab_session',
  MOCK_TEST = 'mock_test',
  MORNING_LOGIN = 'morning_login',
  EVENING_LOGIN = 'evening_login',
  STUDY_TIME = 'study_time',
  STREAK_DAYS = 'streak_days'
}

export enum MissionPeriod {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly'
}

// User mission progress
export interface DailyMissionProgress {
  missions: Record<string, number>; // missionId -> current progress
  completed: boolean;
  createdAt: Date;
}

export interface UserStreakData {
  currentStreak: number;
  longestStreak: number;
  lastMissionDate?: string; // YYYY-MM-DD
  streakFrozen: boolean; // Future feature
  milestones: StreakMilestone[];
}

export interface StreakMilestone {
  days: number;
  achieved: boolean;
  achievedAt?: Date;
  reward: string;
}

// Achievement system
export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  condition: AchievementCondition;
  reward: number; // Points
  rarity: AchievementRarity;
}

export interface AchievementCondition {
  type: 'streak' | 'total_questions' | 'perfect_score' | 'time_spent';
  target: number;
  timeframe?: 'day' | 'week' | 'month' | 'all_time';
}

export type AchievementRarity = 'common' | 'rare' | 'epic' | 'legendary';

export interface UserAchievement {
  achievementId: string;
  unlockedAt: Date;
  progress: number;
  completed: boolean;
}
```

### Database Schema Types
```typescript
// supabase.types.ts - Auto-generated from database
// Generated with: npx supabase gen types typescript --project-id xxx

export interface Database {
  public: {
    Tables: {
      questions: {
        Row: {
          id: string;
          question_text: string;
          question_type: string;
          domain: string;
          skill: string;
          difficulty: number;
          choices: string[];
          correct_answer: string;
          explanation: string;
          passage_text: string | null;
          graph_data: Json | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          question_text: string;
          question_type: string;
          domain: string;
          skill: string;
          difficulty: number;
          choices: string[];
          correct_answer: string;
          explanation: string;
          passage_text?: string | null;
          graph_data?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          question_text?: string;
          question_type?: string;
          domain?: string;
          skill?: string;
          difficulty?: number;
          choices?: string[];
          correct_answer?: string;
          explanation?: string;
          passage_text?: string | null;
          graph_data?: Json | null;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [];
      };
      test_templates: {
        Row: {
          id: string;
          name: string;
          type: string;
          sections: Json;
          time_limit: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          type: string;
          sections: Json;
          time_limit: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          type?: string;
          sections?: Json;
          time_limit?: number;
          created_at?: string;
        };
        Relationships: [];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
```

## Component Interface Types

### Common Component Props
```typescript
// Shared component interfaces
export interface BaseComponentProps {
  class?: string;
  id?: string;
  'data-testid'?: string;
}

export interface LoadingState {
  loading: boolean;
  error: string | null;
}

// Question display components
export interface QuestionComponentProps extends BaseComponentProps {
  question: Question;
  answer?: string;
  onAnswerChange?: (answer: string) => void;
  showExplanation?: boolean;
  disabled?: boolean;
}

// Progress components
export interface ProgressBarProps extends BaseComponentProps {
  current: number;
  target: number;
  color?: string;
  showLabel?: boolean;
  animated?: boolean;
}

// Modal and popup components
export interface ModalProps extends BaseComponentProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  size?: 'small' | 'medium' | 'large';
}
```

## API Response Types

### Server Response Formats
```typescript
// Standard API responses
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  pagination?: PaginationInfo;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Question fetching
export interface QuestionFilters {
  difficulty?: Difficulty;
  questionType?: QuestionType;
  domain?: Domain;
  skill?: string;
  limit?: number;
  offset?: number;
  exclude?: string[]; // Question IDs to exclude
}

export interface QuestionResponse extends ApiResponse {
  data: {
    questions: Question[];
    pagination: PaginationInfo;
  };
}

// Test results
export interface TestSubmission {
  testId: string;
  answers: Record<string, string>;
  timeSpent: number;
  sectionTimes: number[];
  annotations?: Record<string, string[]>;
}

export interface TestResultResponse extends ApiResponse {
  data: {
    result: TestResult;
    analysis: TestAnalysis;
    recommendations: StudyRecommendation[];
  };
}
```

## User Data Types

### User Profile and Progress
```typescript
// Complete user profile
export interface UserProfile {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  
  // Subscription info
  subscriptionType?: 'Pro' | 'Bootcamp' | 'Pro+Bootcamp';
  subscriptionStatus: 'active' | 'cancelled' | 'trial' | 'expired';
  trialEndsAt?: Date;
  
  // Study progress
  currentScore: UserScore;
  totalTestsTaken: number;
  questionsAnswered: number;
  studyTimeMinutes: number;
  accountCreated: Date;
  lastLogin: Date;
  
  // Preferences
  settings: UserSettings;
}

export interface UserScore {
  total: number;
  math: number;
  verbal: number;
  confidence: number;
  lastUpdated: Date;
}

export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  soundEnabled: boolean;
  emailNotifications: boolean;
  reminderTime?: string; // HH:MM format
  timezone: string;
  preferredDifficulty?: Difficulty;
}

// Study analytics
export interface StudyAnalytics {
  userId: string;
  period: 'day' | 'week' | 'month' | 'year';
  questionsAnswered: number;
  accuracy: number;
  studyTimeMinutes: number;
  testsCompleted: number;
  skillProgress: Record<string, SkillProgress>;
  calculatedAt: Date;
}

export interface SkillProgress {
  skill: string;
  questionsAnswered: number;
  accuracy: number;
  improvement: number; // Percentage change
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
}
```

## Utility Types

### Helper and Generic Types
```typescript
// Generic utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type WithTimestamps<T> = T & {
  created_at: string;
  updated_at: string;
};

export type WithUserId<T> = T & {
  userId: string;
};

// Form types
export type FormErrors<T> = Partial<Record<keyof T, string>>;

export interface FormState<T> {
  data: T;
  errors: FormErrors<T>;
  loading: boolean;
  touched: Partial<Record<keyof T, boolean>>;
}

// Event handlers
export type EventHandler<T = Event> = (event: T) => void;
export type AsyncEventHandler<T = Event> = (event: T) => Promise<void>;

// Component children
export type ComponentChildren = import('svelte').ComponentChildren;

// Store types for Svelte
export type Readable<T> = import('svelte/store').Readable<T>;
export type Writable<T> = import('svelte/store').Writable<T>;
```

## Type Guards and Validators

### Runtime Type Checking
```typescript
// Type guards for runtime validation
export function isQuestion(obj: any): obj is Question {
  return (
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.question_text === 'string' &&
    Array.isArray(obj.choices) &&
    typeof obj.correct_answer === 'string'
  );
}

export function isValidDifficulty(value: any): value is Difficulty {
  return typeof value === 'number' && value >= 1 && value <= 5;
}

export function isValidQuestionType(value: any): value is QuestionType {
  return value === 'Reading and Writing' || value === 'Math';
}

// Form validation schemas (using Zod or similar)
export const questionSchema = {
  question_text: (value: string) => value.length > 0 ? null : 'Question text is required',
  difficulty: (value: number) => isValidDifficulty(value) ? null : 'Invalid difficulty',
  choices: (value: string[]) => value.length >= 2 ? null : 'At least 2 choices required'
};
```

## Development Guidelines

### Type Usage Best Practices
```typescript
// 1. Always import types explicitly
import type { Question, TestResult } from '$lib/types';

// 2. Use type assertions carefully
const question = data as Question; // Only when you're certain

// 3. Prefer type guards for runtime checking
if (isQuestion(data)) {
  // TypeScript knows data is Question here
}

// 4. Use generic constraints
function processQuestions<T extends Question>(questions: T[]): T[] {
  return questions.filter(q => q.difficulty > 2);
}

// 5. Define component props with interfaces
interface MyComponentProps {
  question: Question;
  onAnswer?: (answer: string) => void;
  disabled?: boolean;
}
```

## Development Tips

1. **Auto-generation**: Use Supabase CLI to regenerate database types after schema changes
2. **Validation**: Implement runtime validation for external data sources
3. **Type Guards**: Create type guards for complex type checking scenarios
4. **Generic Types**: Use generics for reusable component and utility types
5. **Strict Mode**: Enable strict TypeScript mode for better type safety
6. **Documentation**: Use JSDoc comments for complex types and interfaces
7. **Testing**: Write type tests to ensure type definitions work correctly