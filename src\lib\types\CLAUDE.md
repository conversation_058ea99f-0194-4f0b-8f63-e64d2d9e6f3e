# types/

TypeScript type definitions for the entire application's data structures and API interfaces.

## Type Categories
- **question.types.ts** - Question data structures, test sections, answer formats, difficulty levels, and scoring systems
- **vocab.types.ts** - Vocabulary learning types, spaced repetition data, deck structures, and progress tracking  
- **mission.types.ts** - Gamification system types, mission definitions, progress tracking, and streak data
- **supabase.types.ts** - Auto-generated database schema types from Supabase for type-safe database operations

## Central Exports
- **index.ts** - Re-exports all type definitions for easy importing throughout the application

## Usage
**Import Pattern**: `import { QuestionType, VocabCard, Mission } from '$lib/types'`  
**Type Safety**: Ensures consistency across Firebase, Supabase, and client-side data handling
**Auto-generation**: Supabase types are generated from database schema for accurate type checking

**Integration**: Used throughout components, API routes, and database operations for full TypeScript coverage