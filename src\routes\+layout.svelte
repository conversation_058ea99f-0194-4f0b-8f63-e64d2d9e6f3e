<script lang="ts">
    import '../app.css';
    import { onNavigate } from '$app/navigation';
	import type { Snippet } from 'svelte';

    let { children }: { children: Snippet } = $props();
    
    onNavigate((navigation) => {
        if (!document.startViewTransition) return;

        return new Promise((resolve) => {
            document.startViewTransition(async () => {
                resolve();
                await navigation.complete;
            });
        });
    });
</script>

{@render children?.()}