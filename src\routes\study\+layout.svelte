<script lang="ts">
    import { page } from "$app/state";
    import { user } from '$lib/firebase/auth.svelte.ts';
    import { missionProgress, missionLoading, incrementMissionProgress, subscribeMissionProgress, subscribeStreakData, streakData, missionError } from '$lib/missions/index.ts';
    import NavBar from "$lib/study/NavBar.svelte";
	import { MissionMetric } from "$lib/types/mission.types.ts";
	import type { Unsubscribe } from "@firebase/firestore";
	import { onMount } from "svelte";
	import { innerWidth } from "svelte/reactivity/window";

    let { children, data } = $props();

    let currentPath = $derived(page.url.pathname);

    // Check login missions when user is authenticated AND mission data is ready
    $effect(() => {
        if ($user?.uid && !$missionLoading && $missionProgress !== null) {
            // Check daily login (anytime)
            if (!$missionProgress.missions[MissionMetric.DAILY_LOGIN]) {
                incrementMissionProgress($user.uid, MissionMetric.DAILY_LOGIN);
            }
        }
    });

    let unsubscribeMission = $state<Unsubscribe | null>(null);
    let unsubscribeStreak = $state<Unsubscribe | null>(null);

    /**
     * Initialize mission subscriptions when user changes
     */
    $effect(() => {
        if ($user?.uid) {
            unsubscribeMission = subscribeMissionProgress($user.uid);
            unsubscribeStreak = subscribeStreakData($user.uid);
        } else {
            // Clean up subscriptions when user logs out
            if (unsubscribeMission) unsubscribeMission();
            if (unsubscribeStreak) unsubscribeStreak();

            // Reset stores
            missionProgress.set(null);
            streakData.set(null);
            missionLoading.set(true);
            missionError.set(null);
        }
        return () => {
            if (unsubscribeMission) unsubscribeMission();
            if (unsubscribeStreak) unsubscribeStreak();
        };
    });
</script>

<NavBar {currentPath} role={data.role}/>

<div class="children-container" class:mobile={innerWidth.current < 1024}>
    {@render children()}
</div>

<style>
    .children-container {
        margin: 0 auto;
        margin-left: 4.75rem;
    }

    .mobile {
        margin-left: 0;
        margin-top: 4rem; /* Account for fixed mobile top navbar */
    }
</style>

