{"name": "dsat16", "version": "1.5.0", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "lint": "prettier --plugin-search-dir . --check . && eslint .", "format": "prettier --plugin-search-dir . --write .", "test:unit": "vitest", "test": "npm run test:unit -- --run"}, "devDependencies": {"@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/enhanced-img": "^0.7.0", "@sveltejs/kit": "^2.27.0", "@sveltejs/vite-plugin-svelte": "^6.1.0", "@types/cookie": "^0.6.0", "@types/dom-to-image": "^2.6.7", "@vitest/browser": "^3.2.3", "autoprefixer": "^10.4.21", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.45.1", "playwright": "^1.53.0", "postcss": "^8.5.6", "prettier": "^3.2.5", "prettier-plugin-svelte": "^3.2.6", "supabase": "^2.33.9", "svelte": "^5.37.2", "svelte-check": "^4.1.1", "svelte-turnstile": "^0.11.0", "tailwindcss": "^3.4.17", "vite": "^7.0.6", "vitest": "^3.2.3", "vitest-browser-svelte": "^1.0.0"}, "type": "module", "dependencies": {"@contentful/rich-text-html-renderer": "^17.0.0", "@google/genai": "^0.13.0", "@mailerlite/mailerlite-nodejs": "^1.4.1", "@posthog/ai": "^6.1.2", "@stripe/stripe-js": "^5.2.0", "@supabase/supabase-js": "^2.49.8", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.34.9", "contentful": "^11.4.0", "dompurify": "^3.2.6", "firebase": "^11.0.0", "firebase-admin": "^12.0.0", "firebase-frameworks": "^0.11.2", "layerchart": "^1.0.11", "mime": "^4.0.7", "playwright": "^1.54.1", "posthog-js": "^1.258.5", "posthog-node": "^5.8.1", "puppeteer": "^24.16.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "random-seed": "^0.3.0", "stripe": "^17.4.0", "ts-fsrs": "^5.2.1"}}