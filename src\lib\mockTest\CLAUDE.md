# mockTest/

Core mock test simulation engine with adaptive testing, timer management, and detailed analysis.

## Simulation Engine (/simulation/)
- **SimTop.svelte, SimMid.svelte, SimBottom.svelte** - Main test interface layout components
- **SimReview.svelte** - Answer review and navigation during test
- **SimRef.svelte** - Reference sheet and formula display
- **SimCalc.svelte** - Built-in calculator for math sections
- **Annotate.svelte** - Text highlighting and note-taking integration
- **MinitestIntro.svelte** - Minitest introduction and instructions  
- **SimBreaking.svelte** - Break time countdown between sections
- **Submitting.svelte** - Test submission and processing state

## Question UI (/ui/)
- **Question.svelte, Choices.svelte, AnswerBox.svelte** - Core question display components
- **Passage.svelte** - Reading passage display for verbal sections
- **Graph.svelte** - Mathematical graph and chart rendering
- **MidMath.svelte, MidVerbal.svelte** - Section-specific layouts
- **MidSPR.svelte** - Student-Produced Response (grid-in) questions
- **SimClock.svelte** - Timer display and countdown
- **MarkBar.svelte** - Question navigation and status bar
- **TwoSide.svelte** - Split-screen layout for passage + questions

## Walkthrough System (/walkthrough/)  
- **WalkTop.svelte, WalkMid.svelte, WalkBottom.svelte** - Solution review interface
- **WalkAnswerBox.svelte, WalkChoices.svelte** - Answer explanation components
- **WalkSolution.svelte, WalkMath.svelte** - Detailed solution walkthroughs

**Data Sources**: Supabase question bank, adaptive scoring algorithms  
**Features**: Real-time scoring, section timing, annotation system, detailed analytics
**Routes**: Powers /study/simulations/* pages