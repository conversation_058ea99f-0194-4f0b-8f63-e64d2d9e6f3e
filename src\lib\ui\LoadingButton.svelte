<!-- 
    @component
    Basic button with a loading state after clicking.
    
    Usage:
    ```tsx
    <LoadingButton onclick={function}>Click me!</LoadingButton>
    ```
-->

<script lang="ts">
    import type { Snippet } from 'svelte';
	import type { <PERSON>EventHandler } from 'svelte/elements';
    import Button from './Button.svelte';

    type MouseEventWithCurrentTarget = MouseEvent & { currentTarget: EventTarget & HTMLButtonElement };

    interface Props {
        onclick?: MouseEventHandler<HTMLButtonElement>;
        isSecondary?: boolean;
        fullWidth?: boolean;
        disabled?: boolean;
        type?: 'button' | 'submit' | 'reset';
        children?: Snippet;
    }

    let isLoading = $state(false);

    let {
        onclick = () => {},
        isSecondary = false,
        fullWidth = false,
        disabled = false,
        type = 'button',
        children
    }: Props = $props();

    function handleClick(e: MouseEventWithCurrentTarget) {
        isLoading = true;
        onclick(e);
    }
</script>

<Button onclick={handleClick} {isSecondary} {fullWidth} {disabled} {type}>
    {#if isLoading}
        <div class="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
        Loading...
    {:else}     
        {@render children?.()}
    {/if}
</Button>