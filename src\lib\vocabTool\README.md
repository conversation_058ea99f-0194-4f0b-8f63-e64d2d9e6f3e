# Vocabulary Learning Tool

Spaced repetition vocabulary learning system with AI-generated explanations and adaptive difficulty adjustment.

## Overview

The vocabulary tool implements a scientifically-backed spaced repetition system using the ts-fsrs algorithm. It provides personalized vocabulary learning with AI-generated explanations, progress tracking, and integration with daily missions for consistent practice.

## Architecture

```
vocabTool/
├── WordCard.svelte        # Individual vocabulary card display
└── AnswerButtons.svelte   # Spaced repetition response buttons
```

## Core Features

### Spaced Repetition Learning
- **Algorithm**: Uses ts-fsrs for optimal review scheduling
- **Adaptive Difficulty**: Card difficulty adjusts based on performance
- **Long-term Retention**: Optimized for knowledge retention over time
- **Review Scheduling**: Smart scheduling based on forgetting curve

### AI-Powered Content
- **Dynamic Explanations**: AI-generated definitions and context
- **Example Sentences**: Contextual usage examples
- **Memory Techniques**: Mnemonics and memory aids
- **Etymology**: Word origins and roots when relevant

## Component System

### Word Card Display
```svelte
<!-- WordCard.svelte - Main vocabulary card -->
<script>
  import { geminiAI } from '$lib/server';
  
  let { 
    card,
    showAnswer = false,
    onReveal,
    onAnswer
  } = $props();
  
  let aiExplanation = $state(null);
  let loading = $state(false);
  
  async function generateExplanation() {
    if (aiExplanation) return;
    
    loading = true;
    try {
      aiExplanation = await geminiAI.generateContent(`
        Explain the SAT vocabulary word "${card.word}" in context: "${card.example}"
        
        Provide:
        1. Clear definition appropriate for SAT level
        2. Example sentence using the word correctly  
        3. Common synonyms
        4. Memory technique or mnemonic
        
        Keep explanations concise and student-friendly.
      `);
    } catch (error) {
      console.error('Failed to generate explanation:', error);
    } finally {
      loading = false;
    }
  }
</script>

<div class="vocab-card">
  <div class="card-front">
    <div class="word-header">
      <h2 class="word">{card.word}</h2>
      <span class="difficulty difficulty-{card.difficulty}">
        {card.difficulty}
      </span>
    </div>
    
    <div class="context">
      <p class="example-sentence">{card.example}</p>
      <p class="instruction">What does "{card.word}" mean in this context?</p>
    </div>
    
    {#if !showAnswer}
      <button 
        class="reveal-button"
        onclick={() => {
          onReveal?.();
          generateExplanation();
        }}
      >
        Show Answer
      </button>
    {/if}
  </div>
  
  {#if showAnswer}
    <div class="card-back">
      <div class="definition">
        <h3>Definition</h3>
        <p>{card.definition}</p>
      </div>
      
      {#if card.synonyms?.length}
        <div class="synonyms">
          <h4>Synonyms</h4>
          <div class="synonym-tags">
            {#each card.synonyms as synonym}
              <span class="synonym-tag">{synonym}</span>
            {/each}
          </div>
        </div>
      {/if}
      
      {#if aiExplanation}
        <div class="ai-explanation">
          <h4>AI Explanation</h4>
          <div class="explanation-content">
            {#if loading}
              <div class="loading">Generating explanation...</div>
            {:else}
              <p>{@html aiExplanation.response.text()}</p>
            {/if}
          </div>
        </div>
      {/if}
      
      <div class="card-actions">
        <p class="rating-prompt">How well did you know this word?</p>
        <AnswerButtons onAnswer={onAnswer} />
      </div>
    </div>
  {/if}
</div>
```

### Response System
```svelte
<!-- AnswerButtons.svelte - Spaced repetition rating buttons -->
<script>
  let { onAnswer } = $props();
  
  const ratings = [
    { 
      value: 'again', 
      label: 'Again', 
      description: 'I had no idea',
      color: 'red',
      shortcut: '1'
    },
    { 
      value: 'hard', 
      label: 'Hard', 
      description: 'I struggled with this',
      color: 'orange', 
      shortcut: '2'
    },
    { 
      value: 'good', 
      label: 'Good', 
      description: 'I knew this with effort',
      color: 'blue',
      shortcut: '3'
    },
    { 
      value: 'easy', 
      label: 'Easy', 
      description: 'I knew this perfectly',
      color: 'green',
      shortcut: '4'
    }
  ];
  
  function handleKeydown(event) {
    const key = event.key;
    const rating = ratings.find(r => r.shortcut === key);
    if (rating) {
      onAnswer?.(rating.value);
    }
  }
</script>

<svelte:window on:keydown={handleKeydown} />

<div class="answer-buttons">
  {#each ratings as rating}
    <button 
      class="answer-button color-{rating.color}"
      onclick={() => onAnswer?.(rating.value)}
      title={`${rating.description} (Press ${rating.shortcut})`}
    >
      <span class="button-label">{rating.label}</span>
      <span class="button-description">{rating.description}</span>
      <span class="keyboard-shortcut">{rating.shortcut}</span>
    </button>
  {/each}
</div>

<div class="keyboard-hint">
  <p>Use keyboard shortcuts 1-4 for faster review</p>
</div>
```

## Spaced Repetition Integration

### FSRS Implementation
```typescript
// Integration with ts-fsrs library
import { FSRS, Card, ReviewLog } from 'ts-fsrs';

export class VocabScheduler {
  private fsrs: FSRS;
  
  constructor() {
    // Initialize FSRS with optimized parameters for vocabulary learning
    this.fsrs = new FSRS({
      w: [1.14, 1.01, 5.44, 14.67, 5.3, 1.224, 1.42, 0.8, 0.58, 2.18, 0.174, 0.841, 1.636, 0.9, 0.194, 0.619, 2.896, 0.5, 0.6],
      enable_fuzz: true,
      maximum_interval: 36500, // 100 years max
      enable_short_term: true
    });
  }
  
  // Schedule next review based on user response
  scheduleReview(card: VocabCard, rating: 'again' | 'hard' | 'good' | 'easy'): VocabCard {
    const fsrsCard = this.toFSRSCard(card);
    const schedulingInfo = this.fsrs.repeat(fsrsCard, new Date())[Rating[rating]];
    
    return {
      ...card,
      due: schedulingInfo.card.due,
      stability: schedulingInfo.card.stability,
      difficulty: schedulingInfo.card.difficulty,
      elapsed_days: schedulingInfo.card.elapsed_days,
      scheduled_days: schedulingInfo.card.scheduled_days,
      reps: schedulingInfo.card.reps,
      lapses: schedulingInfo.card.lapses,
      state: schedulingInfo.card.state,
      last_review: new Date()
    };
  }
  
  // Get cards due for review
  getDueCards(cards: VocabCard[]): VocabCard[] {
    const now = new Date();
    return cards
      .filter(card => card.due <= now)
      .sort((a, b) => a.due.getTime() - b.due.getTime());
  }
  
  // Convert between our format and FSRS format
  private toFSRSCard(card: VocabCard): Card {
    return new Card(
      card.due,
      card.stability,
      card.difficulty_sr,
      card.elapsed_days,
      card.scheduled_days,
      card.reps,
      card.lapses,
      card.state
    );
  }
}
```

### Progress Tracking
```typescript
// Track vocabulary learning progress
export interface VocabStats {
  totalCards: number;
  cardsLearned: number;      // Cards with state 'review'
  cardsReviewed: number;     // Cards reviewed today
  accuracy: number;          // Overall accuracy percentage
  studyStreak: number;       // Consecutive study days
  timeSpent: number;         // Minutes spent today
  averageRetention: number;  // Average retention rate
  difficultWords: string[];  // Words with high lapse rate
}

export async function updateVocabProgress(
  userId: string,
  deckId: string, 
  cardId: string,
  rating: string,
  timeSpent: number
): Promise<void> {
  
  const batch = writeBatch(db);
  
  // Update card progress
  const cardRef = doc(db, 'users', userId, 'vocabDecks', deckId, 'cards', cardId);
  batch.update(cardRef, {
    lastReviewed: new Date(),
    totalReviews: increment(1),
    [`ratings.${rating}`]: increment(1)
  });
  
  // Update deck stats
  const deckRef = doc(db, 'users', userId, 'vocabDecks', deckId);
  batch.update(deckRef, {
    cardsReviewedToday: increment(1),
    totalTimeSpent: increment(timeSpent),
    lastStudied: new Date()
  });
  
  // Update daily progress
  const today = new Date().toISOString().split('T')[0];
  const dailyRef = doc(db, 'users', userId, 'vocabProgress', today);
  batch.set(dailyRef, {
    cardsReviewed: increment(1),
    timeSpent: increment(timeSpent),
    date: today
  }, { merge: true });
  
  await batch.commit();
}
```

## Deck Management

### Vocabulary Decks
```typescript
// Predefined vocabulary decks
export const VOCAB_DECKS = {
  high_frequency: {
    id: 'high_frequency',
    name: 'High-Frequency SAT Words',
    description: 'Most common vocabulary words on the SAT',
    estimatedTime: 30,
    difficulty: 'medium' as const,
    category: 'high_frequency' as const
  },
  
  literary: {
    id: 'literary',
    name: 'Literary Terms & Analysis',
    description: 'Words for reading comprehension and analysis',
    estimatedTime: 45,
    difficulty: 'hard' as const,
    category: 'literary' as const
  },
  
  academic: {
    id: 'academic',
    name: 'Academic Vocabulary',
    description: 'Formal academic and scientific terms',
    estimatedTime: 40,
    difficulty: 'hard' as const,
    category: 'academic' as const
  }
};

// Load deck with cards
export async function loadVocabDeck(deckId: string): Promise<VocabDeck> {
  const deckData = VOCAB_DECKS[deckId];
  if (!deckData) throw new Error(`Deck not found: ${deckId}`);
  
  // Load cards from Supabase or predefined data
  const cards = await supabase
    .from('vocab_cards')
    .select('*')
    .eq('deck_id', deckId);
    
  return {
    ...deckData,
    cards: cards.data || []
  };
}
```

## Study Session Management

### Session Flow
```svelte
<!-- Main vocabulary study session -->
<script>
  import { WordCard, AnswerButtons } from '$lib/vocabTool';
  import { incrementMissionProgress } from '$lib/missions';
  
  let currentDeck = $state(null);
  let currentCard = $state(null);
  let showAnswer = $state(false);
  let sessionStats = $state({
    cardsReviewed: 0,
    startTime: new Date(),
    answers: []
  });
  
  const scheduler = new VocabScheduler();
  
  async function startSession(deckId: string) {
    currentDeck = await loadVocabDeck(deckId);
    const dueCards = scheduler.getDueCards(currentDeck.cards);
    
    if (dueCards.length === 0) {
      // No cards due, show completion message
      showSessionComplete();
      return;
    }
    
    currentCard = dueCards[0];
    showAnswer = false;
  }
  
  function revealAnswer() {
    showAnswer = true;
  }
  
  async function handleAnswer(rating: 'again' | 'hard' | 'good' | 'easy') {
    if (!currentCard) return;
    
    // Update card schedule
    const updatedCard = scheduler.scheduleReview(currentCard, rating);
    
    // Save progress
    await updateVocabProgress(
      $user.uid,
      currentDeck.id,
      currentCard.id,
      rating,
      getTimeSpent()
    );
    
    // Update session stats
    sessionStats.cardsReviewed++;
    sessionStats.answers.push({ cardId: currentCard.id, rating, timestamp: new Date() });
    
    // Update mission progress
    await incrementMissionProgress($user.uid, 'vocab_session', 1);
    
    // Load next card
    const remainingCards = scheduler.getDueCards(currentDeck.cards.slice(1));
    if (remainingCards.length > 0) {
      currentCard = remainingCards[0];
      showAnswer = false;
    } else {
      finishSession();
    }
  }
  
  function finishSession() {
    // Show session results
    const accuracy = calculateSessionAccuracy(sessionStats.answers);
    const timeSpent = (new Date().getTime() - sessionStats.startTime.getTime()) / 1000 / 60;
    
    showSessionResults({
      cardsReviewed: sessionStats.cardsReviewed,
      accuracy,
      timeSpent: Math.round(timeSpent),
      nextReview: getNextReviewTime()
    });
  }
</script>

<div class="vocab-session">
  {#if currentCard}
    <div class="session-header">
      <div class="deck-info">
        <h2>{currentDeck.name}</h2>
        <p>{sessionStats.cardsReviewed} reviewed today</p>
      </div>
      
      <div class="session-progress">
        <span>Card {sessionStats.cardsReviewed + 1}</span>
      </div>
    </div>
    
    <WordCard 
      card={currentCard}
      {showAnswer}
      onReveal={revealAnswer}
      onAnswer={handleAnswer}
    />
    
  {:else}
    <div class="session-loading">
      <p>Loading vocabulary cards...</p>
    </div>
  {/if}
</div>
```

## AI-Generated Content

### Dynamic Explanations
```typescript
// AI explanation generation
export async function generateVocabExplanation(
  word: string, 
  context: string
): Promise<VocabExplanation> {
  
  const prompt = `
    Explain the SAT vocabulary word "${word}" as used in this context:
    
    "${context}"
    
    Please provide:
    1. A clear, student-friendly definition
    2. The part of speech (noun, verb, adjective, etc.)
    3. 2-3 common synonyms
    4. An example sentence showing correct usage
    5. A memory technique or mnemonic device
    6. The word's difficulty level (1-5, where 5 is most difficult)
    
    Format the response as JSON.
  `;
  
  try {
    const result = await geminiAI.generateContent(prompt);
    const explanation = JSON.parse(result.response.text());
    
    return {
      word,
      definition: explanation.definition,
      partOfSpeech: explanation.partOfSpeech,
      synonyms: explanation.synonyms,
      example: explanation.example,
      memoryTip: explanation.memoryTip,
      difficulty: explanation.difficulty,
      generatedAt: new Date()
    };
    
  } catch (error) {
    console.error('AI explanation failed:', error);
    
    // Fallback to basic definition
    return {
      word,
      definition: `Definition for ${word} (AI generation failed)`,
      partOfSpeech: 'unknown',
      synonyms: [],
      example: context,
      memoryTip: `Remember: ${word} appears in SAT reading passages`,
      difficulty: 3,
      generatedAt: new Date()
    };
  }
}
```

## Analytics & Progress

### Learning Analytics
```typescript
// Vocabulary learning analytics
export interface VocabAnalytics {
  retention: {
    immediate: number;    // Same-day retention
    shortTerm: number;   // 1-week retention  
    longTerm: number;    // 1-month retention
  };
  
  difficulty: {
    easy: { learned: number; accuracy: number };
    medium: { learned: number; accuracy: number };
    hard: { learned: number; accuracy: number };
  };
  
  categories: {
    highFrequency: VocabCategoryStats;
    literary: VocabCategoryStats;
    academic: VocabCategoryStats;
  };
  
  studyHabits: {
    avgSessionLength: number;  // Minutes
    studyFrequency: number;    // Sessions per week
    bestTimeOfDay: string;     // When user performs best
    consistency: number;       // Study streak score
  };
}

interface VocabCategoryStats {
  totalWords: number;
  wordsLearned: number;
  averageRetention: number;
  timeSpent: number; // Minutes
}
```

## Integration Points

### Mission System Integration
```typescript
// Daily vocabulary missions
export const VOCAB_MISSIONS = {
  daily_vocab_session: {
    target: 1,
    description: 'Complete one vocabulary session',
    reward: 50
  },
  
  vocab_accuracy: {
    target: 80, // 80% accuracy
    description: 'Maintain 80% accuracy in vocab review',
    reward: 25
  },
  
  vocab_streak: {
    target: 7, // 7 consecutive days
    description: 'Study vocabulary for 7 days in a row',
    reward: 100
  }
};
```

### Dashboard Integration
```typescript
// Vocabulary progress for dashboard
export function getVocabProgressSummary(userId: string): VocabProgressSummary {
  return {
    totalWordsLearned: 156,
    wordsReviewedToday: 23,
    currentStreak: 5,
    accuracyThisWeek: 87,
    nextReviewTime: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours
    weakestCategory: 'literary',
    strongestCategory: 'high_frequency'
  };
}
```

## Development Tips

1. **Spaced Repetition**: Trust the FSRS algorithm - avoid manual scheduling
2. **AI Generation**: Cache AI explanations to reduce API calls
3. **Progress Tracking**: Use batch writes for better Firestore performance
4. **Mobile Support**: Optimize for touch interactions and swipe gestures
5. **Offline Support**: Cache vocabulary decks for offline study
6. **Analytics**: Track learning patterns to improve the algorithm
7. **Accessibility**: Ensure screen reader support for vocabulary definitions