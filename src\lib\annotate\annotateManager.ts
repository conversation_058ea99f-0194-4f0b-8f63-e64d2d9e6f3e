import { writable } from "svelte/store";

export const currentAnnotate = writable<Annotate | null>(null);

const colors =  {
    yellow: {
        normal: "#fff9c4",
        hover: "yellow"
    },
    red: {
        normal: "#ffcccc",
        hover: "red"
    },
    selected: {
        normal: 'lightblue',
        hover: "blue"
    }
}

export class AnnotateManager {
    #annotateList: Annotate[];
    #excludedElementList: HTMLElement[];
    #clickHandler: (event: MouseEvent) => void;

    static #currentAnnotate: Annotate | null = null;

    static get currentAnnotate() {
        return this.#currentAnnotate;
    }

    static set currentAnnotate(annotate: Annotate | null) {
        this.#currentAnnotate = annotate;
        currentAnnotate.set(annotate);
    }


    

    constructor(rootList: HTMLElement[], excludeList: HTMLElement[] = []) {
        this.#annotateList = rootList.map(root => new Annotate(root));
        this.#excludedElementList = excludeList;
        this.#clickHandler = (event: MouseEvent) => {
            const target = event.target as HTMLElement;
            if (this.#excludedElementList.some(element => element && element.contains(target))) return;
            AnnotateManager.currentAnnotate?.setFocus(null);
        };

        document.addEventListener("click", this.#clickHandler, { capture: true });
    }

    highlight() {
        if (!this.#annotateList) return;
        this.#annotateList.forEach(annotate => annotate.createHighlight());
    }

    resetRoot(rootList: HTMLElement[]) {
        if (!this.#annotateList) return;
        this.#annotateList.forEach((annotate, i) => annotate.setRoot(rootList[i]));
    }

    resetExcludedElement(excludeList: HTMLElement[]) {
        this.#excludedElementList = excludeList.filter(element => element !== null);
    }

    dispose() {
        // remove listener
        document.removeEventListener("click", this.#clickHandler, { capture: true });

        // clean up
        this.#annotateList?.forEach(annotate => annotate.dispose());
        this.#annotateList = null;
        this.#excludedElementList = null;
        this.#clickHandler = null;
    }
}

class Annotate {
    root: HTMLElement;
    positions: number[];
    marks: Map<string, HTMLElement>;
    marksToHighlights: Map<string, [number, number][]>;
    highlightColors: Map<string, string>;
    markColors: Map<string, string>;
    highlightToFloatingDiv: Map<string, HTMLDivElement>;
    focusedHighlight: [number, number] | null;

    constructor(rootElement: HTMLElement) {
        this.root = rootElement;
        this.positions = [];
        this.marks = new Map();
        this.marksToHighlights = new Map();
        this.highlightColors = new Map();
        this.markColors = new Map();
        this.highlightToFloatingDiv = new Map();
        this.focusedHighlight = null;
    }

    // Get the total offset of a target node and offset within the root node
    getTotalOffset(targetNode, targetOffset) {
        const iterator = document.createNodeIterator(this.root, NodeFilter.SHOW_TEXT);
        let currentNode;
        let totalOffset = 0;
        while ((currentNode = iterator.nextNode())) {
            if (currentNode === targetNode) {
                return totalOffset + targetOffset;
            }
            totalOffset += currentNode.textContent.length;
        }
        return -1; // Not found
    }

    // Get the range of a given start and end position within the root node
    getRange(pos: [number, number]) {
        const [startPos, endPos] = pos;
        let charCount = 0;
        let startNode, startNodeOffset;
        let endNode, endNodeOffset;
    
        if (!this.root || !(this.root instanceof Node)) {
            return null;
        }
    
        const iterator = document.createNodeIterator(this.root, NodeFilter.SHOW_TEXT);
    
        let currentNode;
        while ((currentNode = iterator.nextNode())) {
            const nextCharCount = charCount + currentNode.textContent.length;
            if (!startNode && startPos >= charCount && startPos <= nextCharCount) {
                startNode = currentNode;
                startNodeOffset = startPos - charCount;
            }
            if (!endNode && endPos >= charCount && endPos <= nextCharCount) {
                endNode = currentNode;
                endNodeOffset = endPos - charCount;
                break;
            }
            charCount = nextCharCount;
        }
    
        if (!startNode || !endNode) {
            console.warn("Offsets out of range");
            return null;
        }
    
        const range = document.createRange();
        range.setStart(startNode, startNodeOffset);
        range.setEnd(endNode, endNodeOffset);

        return range;
    }
    
    // Create a mark element with the given start and end offsets within the root node
    createMark(markPos: [number, number], wrapperTag = "mark", focus = false) {
        const markKey = `${markPos[0]},${markPos[1]}`;
        const range = this.getRange(markPos);
        
        if (!range) {
            return;
        }
        
        const extracted = range.extractContents();
        const mark = document.createElement(wrapperTag);
        mark.appendChild(extracted);
        this.marks.set(markKey, mark);
        range.insertNode(mark);
        const highlightList = this.marksToHighlights.get(markKey);
        const minHighlight = highlightList.reduce((min: [number, number], item: [number, number]) => min[1] - min[0] < item[1] - item[0] ? min : item, highlightList[0]);
        const color = this.highlightColors.get(`${minHighlight[0]},${minHighlight[1]}`);
        this.addHoverEffect(markPos);
        this.addClickEvent(markPos);

        if (focus) {
            // Get the floating div
            const floatingDiv = this.highlightToFloatingDiv.get(`${minHighlight[0]},${minHighlight[1]}`);
            floatingDiv.style.display = "none";
            mark.style.backgroundColor = colors[color].hover;
            this.markColors.set(markKey, colors[color].hover);
        } else {
            mark.style.backgroundColor = colors[color].normal;
            this.markColors.set(markKey, colors[color].normal);
        }
    }
    
    // Remove a mark element with the given start and end offsets within the root node
    removeMark(markPos: [number, number]) {
        const markKey = `${markPos[0]},${markPos[1]}`;
        const mark = this.marks.get(markKey);
        this.marks.delete(markKey);
        this.marksToHighlights.delete(markKey);
        if (!mark) return;
        const parent = mark.parentNode;
        while (mark.firstChild) {
            parent.insertBefore(mark.firstChild, mark);
        }
        parent.removeChild(mark);
    }

    // Split the mark element into two marks at the given position
    splitMarks(markPos: [number, number], position) {
        let array = this.marksToHighlights.get(`${markPos[0]},${markPos[1]}`);
        this.marksToHighlights.set(`${markPos[0]},${position}`, [...array]);
        this.marksToHighlights.set(`${position},${markPos[1]}`, [...array]);
        this.removeMark(markPos);
        this.createMark([markPos[0], position]);
        this.createMark([position, markPos[1]]);
    }

    // Merge two mark elements into one
    mergeMarks(markPos: [number, number], position) {
        let array = this.marksToHighlights.get(`${markPos[0]},${position}`);
        this.marksToHighlights.set(`${markPos[0]},${markPos[1]}`, [...array]);
        this.removeMark([markPos[0], position]);
        this.removeMark([position, markPos[1]]);
        this.createMark(markPos);
    }

    // Create floating div
    createFloatingDiv(highlightPos: [number, number] | null, textContent: string) {
        if (!highlightPos) return;
        const floatingStyles = {
            position: 'absolute',
            // bottom: '100%', // place above the span
            // left: '0',      // align with left edge (start)
            // transform: 'translateY(-8px)', // small gap above
            display: 'inline-flex',
            minWidth: '100px',
            padding: '5px',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '10px',
            borderRadius: '10px',
            border: '2px solid #000',
            background: '#FFF',
            color: '#000',
            fontFamily: 'Inter',
            fontSize: '17px',
            fontStyle: 'normal',
            fontWeight: '500',
            lineHeight: 'normal'
        };

        // Create a floating div
        const floatingDiv = document.createElement("div");
        Object.assign(floatingDiv.style, floatingStyles);
        floatingDiv.textContent = textContent;

        const range = this.getRange(highlightPos);
        const rect = range.getBoundingClientRect();
        floatingDiv.style.display = "block";
        document.body.appendChild(floatingDiv);
        const divWidth = floatingDiv.offsetWidth;
        const divHeight = floatingDiv.offsetHeight;
        floatingDiv.style.display = "none"; // hide initially

        floatingDiv.style.left = `${rect.left + rect.width / 2 - divWidth / 2 + window.scrollX}px`;
        floatingDiv.style.top = `${rect.top - divHeight - 20 + window.scrollY}px`; // 8px above the target

        this.highlightToFloatingDiv.set(`${highlightPos[0]},${highlightPos[1]}`, floatingDiv);
    }

    // Add a hover effect to the mark element
    addHoverEffect(markPos: [number, number]) {
        const markKey = `${markPos[0]},${markPos[1]}`;
        const mark = this.marks.get(markKey);
        const highlightList = this.marksToHighlights.get(markKey);
        if (!mark || !highlightList) return;

        // Get the smallest highlight
        const minHighlight = highlightList.reduce((min: [number, number], item: [number, number]) => min[1] - min[0] < item[1] - item[0] ? min : item, highlightList[0]);
        const highlightKey = `${minHighlight[0]},${minHighlight[1]}`;
        const color = this.highlightColors.get(highlightKey);
        const floatingDiv = this.highlightToFloatingDiv.get(highlightKey);


        // Create new handlers
        const enter = () => {
            for (let i = 0; i < this.positions.length - 1; i++) {
                if (this.positions[i] >= minHighlight[0] && this.positions[i + 1] <= minHighlight[1]) {
                    const m = this.marks.get(`${this.positions[i]},${this.positions[i + 1]}`);
                    if (m) m.style.backgroundColor = colors[color].hover;
                }
            }
            if (floatingDiv.textContent !== "") {
                const range = this.getRange(minHighlight);
                const rect = range.getBoundingClientRect();
                floatingDiv.style.display = "block";
                floatingDiv.style.left = `${rect.left + rect.width / 2 - floatingDiv.offsetWidth / 2 + window.scrollX}px`;
                floatingDiv.style.top = `${rect.top - floatingDiv.offsetHeight - 20 + window.scrollY}px`;
            }
        };

        const leave = () => {
            for (let i = 0; i < this.positions.length - 1; i++) {
                if (this.positions[i] >= minHighlight[0] && this.positions[i + 1] <= minHighlight[1]) {
                    const m = this.marks.get(`${this.positions[i]},${this.positions[i + 1]}`);
                    if (m) m.style.backgroundColor = this.markColors.get(`${this.positions[i]},${this.positions[i + 1]}`);
                }
            }
            floatingDiv.style.display = "none";
        };

        // Add new listeners
        mark.addEventListener("mouseenter", enter);
        mark.addEventListener("mouseleave", leave);
    }

    // Add a click event to the mark element
    addClickEvent(markPos: [number, number]) {
        const [startPos, endPos] = markPos;
        const mark = this.marks.get(`${startPos},${endPos}`);
        const highlightList = this.marksToHighlights.get(`${startPos},${endPos}`);
        const minHighlight: [number, number] = highlightList.reduce((min, item) => min[1] - min[0] < item[1] - item[0] ? min : item, highlightList[0]);

        mark.addEventListener("click", () => {
            this.setFocus(minHighlight);
        });
    }
    
    // Add a highlight to the mark element
    addHighlightToMark(markPos: [number, number], highlightPos: [number, number]) {
        const key = `${markPos[0]},${markPos[1]}`;
        (this.marksToHighlights.get(key) ?? this.marksToHighlights.set(key, []).get(key)).push(highlightPos);
    }

    // Update the mark element with the given start and end offsets within the root node
    updateMarks(markPos: [number, number], focus = false) {
        const key = `${markPos[0]},${markPos[1]}`;
        if (this.marks.has(key)) {
            const tempHighlights = [...(this.marksToHighlights.get(key) || [])];
            this.removeMark(markPos);
            this.marksToHighlights.set(key, tempHighlights);
        }
        this.createMark(markPos, "mark", focus);
    }

    // Add highlight
    addHighlights(highlightPos: [number, number]) {
        const [startPos, endPos] = highlightPos;
        let startIndex = this.positions.findIndex(x => x >= startPos);
        let newStartPos = true;

        if (startIndex === -1) {
            this.positions.push(startPos);
            startIndex = this.positions.length-1;
        } else if (this.positions[startIndex] !== startPos) {
            this.positions.splice(startIndex, 0, startPos);
        } else {
            newStartPos = false;
        }

        if (newStartPos && startIndex > 0 && startIndex < this.positions.length - 1 && this.marks.has(`${this.positions[startIndex - 1]},${this.positions[startIndex + 1]}`)) {
            this.splitMarks([this.positions[startIndex - 1], this.positions[startIndex + 1]], startPos);
        }

        let endIndex = this.positions.findIndex(x => x >= endPos);
        let newEndPos = true;

        if (endIndex === -1) {
            this.positions.push(endPos);
            endIndex = this.positions.length - 1;
        } else if (this.positions[endIndex] !== endPos) {
            this.positions.splice(endIndex, 0, endPos);
        } else {
            newEndPos = false;
        }

        if (newEndPos && endIndex < this.positions.length - 1 && this.marks.has(`${this.positions[endIndex - 1]},${this.positions[endIndex + 1]}`)) {
            this.splitMarks([this.positions[endIndex - 1], this.positions[endIndex + 1]], endPos);
        }

        for (let i = startIndex; i < endIndex; i++) {
            this.addHighlightToMark([this.positions[i], this.positions[i + 1]], highlightPos);
        }

        this.normalizeDeep(this.root);
    }

    // Normalize the positions array and the marks within the root node
    normalizeMarks(startIndex, endIndex) {
        // Remove marks that have no highlights
        for (let i = startIndex; i < endIndex; i++) {
            const key = `${this.positions[i]},${this.positions[i + 1]}`;
            const highlightsList = this.marksToHighlights.get(key);
            if (highlightsList.length === 0) {
                this.removeMark([this.positions[i], this.positions[i + 1]]);
            } else {
                this.updateMarks([this.positions[i], this.positions[i + 1]]);
            }
        }

        // Remove positions that are not in use
        for (let i = startIndex; i <= endIndex; i++) {
            if (startIndex === endIndex) {
                this.positions.splice(i, 1);
            }

            const prev = this.positions[i - 1];
            const curr = this.positions[i];
            const next = this.positions[i + 1];

            const hasPrev = prev !== undefined && this.marks.has(`${prev},${curr}`);
            const hasNext = next !== undefined && this.marks.has(`${curr},${next}`);

            if (!hasPrev && !hasNext) {
                this.positions.splice(i, 1);
                i--;
                endIndex--;
            }
        }

        // Merge marks that belong to same highlights
        let prev = null;
        for (let i = startIndex - 1; i <= endIndex; i++) {
            const cur = this.positions[i];
            const next = this.positions[i + 1];
            if (cur === undefined || next === undefined || !this.marks.has(`${cur},${next}`)) {
                prev = null;
                continue;
            }

            if (prev === null) {
                prev = cur;
                continue;
            }

            const keyPrev = `${prev},${cur}`;
            const keyCurr = `${cur},${next}`;
            const highlightsList1 = this.marksToHighlights.get(keyPrev);
            const highlightsList2 = this.marksToHighlights.get(keyCurr);
            const isSame = highlightsList1.length === highlightsList2.length && highlightsList1.every(item => highlightsList2.some(item2 => item[0] === item2[0] && item[1] === item2[1]));
            if (isSame) {
                this.mergeMarks([prev, next], cur);
                this.positions.splice(i, 1);
                i--;
                endIndex--;
            } else {
                prev = cur;
            }
        }

        this.normalizeDeep(this.root);
    }

    // Normalize the DOM tree
    normalizeDeep(root) {
        let prev = null;

        let i = 0;
        while (i < root.childNodes.length) {
            const node = root.childNodes[i];
            if (node instanceof HTMLElement) {
                // Recursively normalize children first
                this.normalizeDeep(node);

                // Remove empty elements
                if (node.childNodes.length === 0) {
                    root.removeChild(node);
                    continue;
                }

                // Merge adjacent elements with same tag and no attributes
                if (prev &&
                    prev instanceof HTMLElement &&
                    prev.tagName === node.tagName &&
                    prev.attributes.length === 0 &&
                    node.attributes.length === 0
                ) {
                    while (node.firstChild) {
                        prev.appendChild(node.firstChild);
                    }
                    root.removeChild(node);
                    continue;
                }
            }

            // Save this node as previous
            prev = node;
            i++;
        }

        root.normalize();
    }

    // Remove a highlight from the mark element
    removeHighlight(highlightPos: [number, number]) {
        const key = `${highlightPos[0]},${highlightPos[1]}`;
        let startIndex = this.positions.findIndex(x => x === highlightPos[0]);
        let endIndex = this.positions.findIndex(x => x === highlightPos[1]);
        this.highlightColors.delete(key);
        this.highlightToFloatingDiv.get(key)?.remove();
        this.highlightToFloatingDiv.delete(key);

        for (let i = startIndex; i < endIndex; i++) {
            const key = `${this.positions[i]},${this.positions[i + 1]}`;
            let highlightsList = this.marksToHighlights.get(key);
            highlightsList = highlightsList.filter(item => item[0] !== highlightPos[0] || item[1] !== highlightPos[1]);
            this.marksToHighlights.set(key, highlightsList);
        }
        this.normalizeMarks(startIndex, endIndex);
    }

    // Highlight the selected text
    createHighlight(color: string = "yellow"): boolean {
        // Get the selection
        if (this.root === null) return false;
        const selection = document.getSelection();
        if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);

            // Get the total offset of the start and end of the range
            const startPos = this.getTotalOffset(range.startContainer, range.startOffset);
            const endPos = this.getTotalOffset(range.endContainer, range.endOffset);
            
            if (startPos === -1 || endPos === -1 || startPos === endPos) {
                return false;
            }

            const highlightPos: [number, number] = [startPos, endPos];

            this.highlightColors.set(`${startPos},${endPos}`, color);
            this.createFloatingDiv(highlightPos, "");
            this.addHighlights(highlightPos);
            this.setFocus(highlightPos);

            // Collapse the range to the end
            range.collapse(false);
            return true;
        }
        return false;
    }

    // Update focus color
    updateBlur(highlightPos: [number, number] | null) {
        if (!highlightPos) return;
        let startIndex = this.positions.findIndex(x => x >= highlightPos[0]);
        let endIndex = this.positions.findIndex(x => x >= highlightPos[1]);
        for (let i = startIndex; i < endIndex; i++) {
            this.updateMarks([this.positions[i], this.positions[i + 1]]);
        }
    }

    updateFocus(highlightPos: [number, number] | null) {
        if (!highlightPos) return;
        let startIndex = this.positions.findIndex(x => x >= highlightPos[0]);
        let endIndex = this.positions.findIndex(x => x >= highlightPos[1]);
        for (let i = startIndex; i < endIndex; i++) {
            this.updateMarks([this.positions[i], this.positions[i + 1]], true);
        }
    }

    setFocus(highlightPos: [number, number] | null) {
        // If there exist current selection
        this.updateBlur(this.focusedHighlight);
        this.focusedHighlight = highlightPos;
        this.updateFocus(this.focusedHighlight);
        AnnotateManager.currentAnnotate = highlightPos ? this : null;
    }

    getCommentText() {
        if (this.focusedHighlight) {
            const floatingDiv = this.highlightToFloatingDiv.get(`${this.focusedHighlight[0]},${this.focusedHighlight[1]}`);
            return floatingDiv.textContent;
        }
        return null;
    }

    setCommentText(text: string) {
        const floatingDiv = this.highlightToFloatingDiv.get(`${this.focusedHighlight[0]},${this.focusedHighlight[1]}`);
        floatingDiv.textContent = text;
    }

    getSelectedText() {
        const range = this.getRange(this.focusedHighlight);
        return range.toString();
    }

    deleteHighlight() {
        this.removeHighlight(this.focusedHighlight);
        this.focusedHighlight = null;
        this.setFocus(null);
    }

    rerenderHighlights() {
        this.positions.forEach((pos, i) => {
            if (i === this.positions.length - 1) return;
            const nextPos = this.positions[i + 1];
            if (this.marks.has(`${pos},${nextPos}`)) this.createMark([pos, nextPos]);
        });
    }

    setRoot(rootElement: HTMLElement) {
        this.root = rootElement;
        this.rerenderHighlights();
    }

    dispose() {
        this.positions.length = 0;
        this.marks.clear();
        this.marksToHighlights.clear();
        this.highlightColors.clear();
        this.markColors.clear();
        for (const floatingDiv of this.highlightToFloatingDiv.values()) {
            floatingDiv.remove();
        }
        this.highlightToFloatingDiv.clear();
        this.root = null;
    }
}

