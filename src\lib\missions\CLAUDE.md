# missions/

Gamification system with daily missions, streak tracking, and progress rewards.

## State Management
- **mission.store.ts** - Reactive mission progress, streak data, completion percentages using Svelte 5 runes
- **index.ts** - Central exports for stores, catalog, and engine functions

## Mission System
- **missionCatalog.ts** - Mission definitions, metadata, routes, and button text generation
- **missionEngine.ts** - Business logic for progress tracking, streak calculation, and data migration

## Key Features
- **Daily Missions**: Question bank practice, vocab learning, mock tests
- **Streak Tracking**: Consecutive study day counting with milestone rewards
- **Progress Persistence**: Firestore integration with real-time sync
- **PostHog Integration**: Achievement tracking and user property updates
- **Auto-reset**: Daily mission reset and streak validation

## Exported Functions
**Stores**: missionProgress, streakData, missionLoading, missionError, completion percentages  
**Catalog**: MISSION_CATALOG, mission lookups, daily mission filtering
**Engine**: Progress increment, streak calculation, data migration, initialization

**Integration**: Used by dashboard components and study area for gamification features