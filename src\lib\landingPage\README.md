# Landing Page System

Marketing website components for user acquisition, conversion optimization, and brand messaging across public-facing pages.

## Overview

The landing page system provides a complete marketing website with conversion-optimized components, social proof elements, and feature demonstrations. It's designed for the (marketing) route group and focuses on converting visitors to subscribers.

## Architecture

```
landingPage/
├── index.ts                # Main layout exports
├── NavBar.svelte          # Navigation with auth integration
├── Footer.svelte          # Site footer with links
├── SectionWrapper.svelte  # Consistent section styling
├── SignUpButton.svelte    # Primary CTA component
└── home/                  # Homepage-specific sections
    ├── index.ts           # Home section exports
    ├── Hero.svelte        # Main value proposition
    ├── Banner.svelte      # Promotional announcements
    ├── SocialProof.svelte # Testimonials and reviews
    ├── CoreFeatures.svelte # Key feature highlights
    ├── Comparison.svelte  # Competitive comparison
    ├── HowItWorks.svelte  # Process explanation
    ├── Demo.svelte        # Product demonstration
    ├── CTA.svelte         # Call-to-action section
    └── Manifesto.svelte   # Brand mission statement
```

## Core Components

### Layout Components
```svelte
<!-- Main layout components -->
<script>
  import { 
    Nav<PERSON>, 
    <PERSON><PERSON>, 
    <PERSON><PERSON><PERSON><PERSON>, 
    SignUpButton 
  } from '$lib/landingPage';
</script>

<Navbar />

<main>
  <SectionWrapper>
    <!-- Page content -->
  </SectionWrapper>
</main>

<Footer />
```

### Hero Section
```svelte
<!-- Hero.svelte - Main value proposition -->
<script>
  import { Hero } from '$lib/landingPage/home';
  
  let heroProps = {
    headline: "You don't need to burn out to get a good score",
    subheadline: "The only gamified SAT platform that helps you beat the test while enjoying the process.",
    features: [
      "See clear improvements over time",
      "Keep you away from feeling overwhelmed and demotivated", 
      "Get you addicted to learning instead of hating it"
    ],
    primaryCTA: "Get Addicted",
    secondaryCTA: "Explore More",
    riskReversal: "Money back guarantee <b>(it's free)</b>"
  };
</script>

<Hero {...heroProps} />
```

## Homepage Structure

### Complete Homepage Layout
```svelte
<!-- +page.svelte in (marketing) route -->
<script>
  import { 
    Banner,
    Hero, 
    SocialProof,
    CoreFeatures,
    Comparison,
    HowItWorks,
    Demo,
    CTA,
    Manifesto
  } from '$lib/landingPage/home';
</script>

<Banner />
<Hero />
<SocialProof />
<CoreFeatures />
<Comparison />
<HowItWorks />
<Demo />
<Manifesto />
<CTA />
```

### Social Proof Integration
```svelte
<!-- SocialProof.svelte -->
<script>
  let testimonials = [
    {
      name: "Sarah Chen",
      score: "1540",
      improvement: "+240 points",
      quote: "The gamified approach made studying actually fun!",
      image: "/testimonials/sarah.jpg"
    },
    // ... more testimonials
  ];
  
  let stats = {
    studentsHelped: "10,000+",
    averageImprovement: "180 points",
    successRate: "94%"
  };
</script>

<section class="social-proof">
  <div class="stats-grid">
    <div class="stat">
      <h3>{stats.studentsHelped}</h3>
      <p>Students Helped</p>
    </div>
    <!-- More stats -->
  </div>
  
  <div class="testimonials-grid">
    {#each testimonials as testimonial}
      <div class="testimonial-card">
        <blockquote>"{testimonial.quote}"</blockquote>
        <cite>
          {testimonial.name} - {testimonial.score} ({testimonial.improvement})
        </cite>
      </div>
    {/each}
  </div>
</section>
```

## Feature Demonstration

### Core Features Section
```svelte
<!-- CoreFeatures.svelte -->
<script>
  let features = [
    {
      title: "Adaptive Mock Tests",
      description: "AI-powered tests that adapt to your skill level",
      icon: "🎯",
      image: "/assets/home/<USER>"
    },
    {
      title: "Gamified Learning", 
      description: "Daily missions and streak tracking keep you motivated",
      icon: "🎮",
      image: "/assets/home/<USER>"
    },
    {
      title: "Smart Vocabulary",
      description: "Spaced repetition system for efficient vocab learning", 
      icon: "📚",
      image: "/assets/home/<USER>"
    }
  ];
</script>

<section class="core-features">
  {#each features as feature}
    <div class="feature-card">
      <div class="feature-icon">{feature.icon}</div>
      <h3>{feature.title}</h3>
      <p>{feature.description}</p>
      <img src={feature.image} alt={feature.title} />
    </div>
  {/each}
</section>
```

### How It Works Process
```svelte
<!-- HowItWorks.svelte -->
<script>
  let steps = [
    {
      step: 1,
      title: "Take Diagnostic Test",
      description: "Identify your strengths and weaknesses",
      image: "/assets/step-1.png"
    },
    {
      step: 2, 
      title: "Follow Personalized Plan",
      description: "AI generates custom study recommendations",
      image: "/assets/step-2.png"
    },
    {
      step: 3,
      title: "Track Your Progress", 
      description: "Watch your scores improve over time",
      image: "/assets/dashboard.png"
    }
  ];
</script>

<section class="how-it-works">
  <h2>How It Works</h2>
  <div class="steps-flow">
    {#each steps as step}
      <div class="step">
        <div class="step-number">{step.step}</div>
        <h3>{step.title}</h3>
        <p>{step.description}</p>
        <img src={step.image} alt="Step {step.step}" />
      </div>
    {/each}
  </div>
</section>
```

## Conversion Optimization

### Call-to-Action Components
```svelte
<!-- SignUpButton.svelte - Reusable CTA -->
<script>
  import { Button } from '$lib/ui';
  import { user } from '$lib/firebase/auth.svelte';
  import { goto } from '$app/navigation';
  
  let { 
    text = "Get Started Free",
    variant = "primary",
    size = "large",
    href = "/sign-up",
    trackingEvent = "cta_clicked"
  } = $props();
  
  function handleClick() {
    // Analytics tracking
    if (typeof posthog !== 'undefined') {
      posthog.capture(trackingEvent, {
        cta_text: text,
        cta_location: 'landing_page'
      });
    }
    
    // Redirect logic
    if ($user) {
      goto('/study');
    } else {
      goto(href);
    }
  }
</script>

<Button {variant} {size} onclick={handleClick}>
  {text}
</Button>
```

### Competitive Comparison
```svelte
<!-- Comparison.svelte -->
<script>
  let comparisonData = {
    features: [
      { name: "Adaptive Testing", us: true, competitor1: false, competitor2: true },
      { name: "Gamification", us: true, competitor1: false, competitor2: false },
      { name: "AI Study Plans", us: true, competitor1: false, competitor2: false },
      { name: "Real-time Analytics", us: true, competitor1: true, competitor2: false },
      { name: "Mobile App", us: true, competitor1: true, competitor2: true },
    ],
    pricing: {
      us: "$0/month",
      competitor1: "$39/month", 
      competitor2: "$79/month"
    }
  };
</script>

<section class="comparison">
  <h2>Why Choose Us?</h2>
  <div class="comparison-table">
    <table>
      <thead>
        <tr>
          <th>Feature</th>
          <th>Us</th>
          <th>Competitor A</th>
          <th>Competitor B</th>
        </tr>
      </thead>
      <tbody>
        {#each comparisonData.features as feature}
          <tr>
            <td>{feature.name}</td>
            <td>{feature.us ? '✅' : '❌'}</td>
            <td>{feature.competitor1 ? '✅' : '❌'}</td>
            <td>{feature.competitor2 ? '✅' : '❌'}</td>
          </tr>
        {/each}
      </tbody>
    </table>
  </div>
</section>
```

## Navigation & Layout

### Responsive Navigation
```svelte
<!-- NavBar.svelte -->
<script>
  import { user } from '$lib/firebase/auth.svelte';
  import { Button } from '$lib/ui';
  
  let mobileMenuOpen = $state(false);
  
  let navigationItems = [
    { label: "Features", href: "/#features" },
    { label: "How It Works", href: "/#how-it-works" },
    { label: "Pricing", href: "/pricing" },
    { label: "About", href: "/about" }
  ];
</script>

<nav class="navbar">
  <div class="nav-container">
    <a href="/" class="logo">
      <img src="/logo.svg" alt="DSAT16" />
    </a>
    
    <!-- Desktop navigation -->
    <div class="nav-links desktop-only">
      {#each navigationItems as item}
        <a href={item.href}>{item.label}</a>
      {/each}
    </div>
    
    <!-- Auth section -->
    <div class="nav-auth">
      {#if $user}
        <Button href="/study" variant="primary">
          Dashboard
        </Button>
      {:else}
        <Button href="/sign-in" variant="ghost">
          Sign In
        </Button>
        <Button href="/sign-up" variant="primary">
          Get Started
        </Button>
      {/if}
    </div>
    
    <!-- Mobile menu button -->
    <button 
      class="mobile-menu-btn"
      onclick={() => mobileMenuOpen = !mobileMenuOpen}
    >
      ☰
    </button>
  </div>
  
  <!-- Mobile menu -->
  {#if mobileMenuOpen}
    <div class="mobile-menu">
      {#each navigationItems as item}
        <a href={item.href}>{item.label}</a>
      {/each}
    </div>
  {/if}
</nav>
```

## Analytics Integration

### Conversion Tracking
```typescript
// Track key conversion events
import posthog from 'posthog-js';

// Hero CTA clicks
const trackHeroCTA = () => {
  posthog.capture('hero_cta_clicked', {
    button_text: 'Get Addicted',
    page_section: 'hero'
  });
};

// Feature exploration
const trackFeatureView = (featureName: string) => {
  posthog.capture('feature_viewed', {
    feature_name: featureName,
    page: 'landing'
  });
};

// Pricing page visits
const trackPricingVisit = () => {
  posthog.capture('pricing_page_visited', {
    source: 'landing_page'
  });
};
```

## SEO Optimization

### Meta Tags & Structured Data
```svelte
<!-- In +page.svelte head -->
<svelte:head>
  <title>DSAT16 - Gamified SAT Prep That Actually Works</title>
  <meta name="description" content="The only SAT prep platform that makes studying addictive. Adaptive tests, AI study plans, and gamification to boost your score without burnout." />
  <meta name="keywords" content="SAT prep, test preparation, adaptive learning, gamified education" />
  
  <!-- Open Graph -->
  <meta property="og:title" content="DSAT16 - Gamified SAT Prep" />
  <meta property="og:description" content="Beat the SAT while enjoying the process" />
  <meta property="og:image" content="/og-image.jpg" />
  
  <!-- Structured Data -->
  <script type="application/ld+json">
    {JSON.stringify({
      "@context": "https://schema.org",
      "@type": "EducationalOrganization",
      "name": "DSAT16",
      "description": "Gamified SAT preparation platform",
      "url": "https://dsat16.com"
    })}
  </script>
</svelte:head>
```

## Development Tips

1. **A/B Testing**: Use feature flags for testing different hero messages
2. **Performance**: Lazy load images and optimize for Core Web Vitals
3. **Accessibility**: Ensure proper heading hierarchy and alt text
4. **Mobile-first**: Design for mobile and enhance for desktop
5. **Conversion Rate**: Test different CTA copy and placement
6. **Analytics**: Track scroll depth and engagement metrics
7. **Social Proof**: Regularly update testimonials and stats