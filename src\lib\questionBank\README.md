# Question Bank System

Individual question practice interface providing targeted skill development with filtering, progress tracking, and detailed explanations.

## Overview

The question bank system allows students to practice individual questions outside of full mock tests. It features intelligent filtering, progress tracking, detailed explanations, and integration with the missions system for daily practice goals.

## Architecture

```
questionBank/
└── QuestionBank.svelte    # Main question bank interface
```