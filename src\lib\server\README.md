# Server Services

Backend service integrations and server-side utilities for external APIs, databases, and third-party services.

## Overview

The server module provides centralized access to all backend services including Firebase Admin SDK, Supabase database, Contentful CMS, Redis caching, and AI services. All services are configured for asia-southeast1 deployment and optimized for serverless environments.

## Architecture

```
server/
├── index.ts        # Central service exports
├── admin.ts        # Firebase Admin SDK (auth & Firestore)
├── supabase.ts     # Supabase client for questions database
├── contentful.ts   # Contentful CMS for bootcamp content
├── redis.ts        # Upstash Redis for caching & rate limiting
└── ai.ts          # Google Generative AI (Gemini)
```

## Service Integration

### Central Exports
```typescript
// index.ts - Single import point
import { 
  client,         // Contentful CMS client
  adminDB,        // Firebase Admin Firestore
  adminAuth,      // Firebase Admin Auth
  supabase,       // Supabase database client
  redis,          // Redis cache client
  geminiAI        // Google AI client
} from '$lib/server';

// Usage in API routes
export async function POST({ request, locals }) {
  const user = locals.user;
  
  // Verify authentication
  const verified = await adminAuth.verifyIdToken(idToken);
  
  // Fetch questions from Supabase
  const questions = await supabase
    .from('questions')
    .select('*')
    .limit(10);
  
  // Cache results in Redis
  await redis.setex(`questions:${user.uid}`, 300, JSON.stringify(questions.data));
  
  return json({ questions: questions.data });
}
```

## Firebase Admin SDK

### Authentication & Authorization
```typescript
// admin.ts - Server-side Firebase operations
import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';

// Initialize Admin SDK
if (!getApps().length) {
  initializeApp({
    credential: cert({
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n')
    })
  });
}

export const adminAuth = getAuth();
export const adminDB = getFirestore();

// Custom claims management
export async function updateUserRoles(uid: string, roles: string[]) {
  await adminAuth.setCustomUserClaims(uid, { roles });
}

// Server-side token verification
export async function verifyUserToken(idToken: string) {
  try {
    const decodedToken = await adminAuth.verifyIdToken(idToken);
    return {
      uid: decodedToken.uid,
      email: decodedToken.email,
      roles: decodedToken.roles || []
    };
  } catch (error) {
    return null;
  }
}
```

### Firestore Operations
```typescript
// Server-side database operations
export async function getUserProfile(uid: string) {
  const doc = await adminDB.collection('users').doc(uid).get();
  return doc.exists ? doc.data() : null;
}

export async function saveTestResults(uid: string, results: TestResults) {
  const batch = adminDB.batch();
  
  // Save test results
  const testRef = adminDB.collection('users').doc(uid).collection('tests').doc();
  batch.set(testRef, {
    ...results,
    createdAt: new Date()
  });
  
  // Update user stats
  const userRef = adminDB.collection('users').doc(uid);
  batch.update(userRef, {
    testsCompleted: adminDB.FieldValue.increment(1),
    totalScore: results.totalScore,
    lastTestDate: new Date()
  });
  
  await batch.commit();
  return testRef.id;
}
```

## Supabase Database

### Question Bank Operations
```typescript
// supabase.ts - Question database operations
import { createClient } from '@supabase/supabase-js';
import type { Database } from '$lib/types/supabase.types';

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

export const supabase = createClient<Database>(supabaseUrl, supabaseKey);

// Fetch questions with filters
export async function getQuestions(filters: QuestionFilters) {
  let query = supabase
    .from('questions')
    .select(`
      id,
      question_text,
      question_type,
      difficulty,
      choices,
      correct_answer,
      explanation,
      passage_text,
      domain,
      skill
    `);
  
  if (filters.difficulty) {
    query = query.eq('difficulty', filters.difficulty);
  }
  
  if (filters.questionType) {
    query = query.eq('question_type', filters.questionType);
  }
  
  if (filters.domain) {
    query = query.eq('domain', filters.domain);
  }
  
  const { data, error } = await query;
  
  if (error) throw new Error(`Failed to fetch questions: ${error.message}`);
  return data;
}

// Get test template
export async function getTestTemplate(testId: string) {
  const { data, error } = await supabase
    .from('test_templates')
    .select(`
      *,
      sections(
        *,
        questions(*)
      )
    `)
    .eq('id', testId)
    .single();
    
  if (error) throw new Error(`Failed to fetch test: ${error.message}`);
  return data;
}
```

### Database Schema Types
```typescript
// Supabase generated types provide full type safety
interface Question {
  id: string;
  question_text: string;
  question_type: 'Reading and Writing' | 'Math';
  difficulty: number;
  choices: string[];
  correct_answer: string;
  explanation: string;
  passage_text?: string;
  domain: string;
  skill: string;
  created_at: string;
}

// Type-safe database operations
const questions: Question[] = await supabase
  .from('questions')
  .select('*')
  .eq('difficulty', 3)
  .then(({ data }) => data || []);
```

## Contentful CMS

### Content Management
```typescript
// contentful.ts - CMS integration for bootcamp content
import { createClient } from 'contentful';

export const client = createClient({
  space: process.env.CONTENTFUL_SPACE_ID!,
  accessToken: process.env.CONTENTFUL_ACCESS_TOKEN!,
  environment: process.env.CONTENTFUL_ENVIRONMENT || 'master'
});

// Fetch bootcamp lectures
export async function getBootcampLectures() {
  const entries = await client.getEntries({
    content_type: 'bootcampLecture',
    order: 'fields.order'
  });
  
  return entries.items.map(item => ({
    id: item.sys.id,
    title: item.fields.title,
    slug: item.fields.slug,
    videoUrl: item.fields.videoUrl,
    description: item.fields.description,
    order: item.fields.order,
    hasAdditionalResources: item.fields.hasAdditionalResources
  }));
}

// Get lecture by slug
export async function getLectureBySlug(slug: string) {
  const entries = await client.getEntries({
    content_type: 'bootcampLecture',
    'fields.slug': slug,
    limit: 1
  });
  
  if (entries.items.length === 0) {
    throw new Error(`Lecture not found: ${slug}`);
  }
  
  return entries.items[0];
}

// Fetch additional resources
export async function getAdditionalResources(lectureSlug: string) {
  const entries = await client.getEntries({
    content_type: 'additionalResource',
    'fields.lectureSlug': lectureSlug
  });
  
  return entries.items.map(item => ({
    title: item.fields.title,
    type: item.fields.type,
    url: item.fields.url,
    description: item.fields.description
  }));
}
```

## Redis Caching

### Performance Optimization
```typescript
// redis.ts - Upstash Redis for caching
import { Redis } from '@upstash/redis';

export const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!
});

// Cache frequently accessed data
export async function getCachedQuestions(filters: string) {
  const cacheKey = `questions:${filters}`;
  
  // Try cache first
  const cached = await redis.get(cacheKey);
  if (cached) {
    return JSON.parse(cached as string);
  }
  
  // Fetch from database
  const questions = await getQuestions(JSON.parse(filters));
  
  // Cache for 5 minutes
  await redis.setex(cacheKey, 300, JSON.stringify(questions));
  
  return questions;
}

// Rate limiting
export async function checkRateLimit(userId: string, action: string): Promise<boolean> {
  const key = `rateLimit:${userId}:${action}`;
  const current = await redis.incr(key);
  
  if (current === 1) {
    // First request, set expiry
    await redis.expire(key, 3600); // 1 hour window
  }
  
  // Allow up to 100 requests per hour per action
  return current <= 100;
}

// Session management
export async function storeSession(sessionId: string, data: any) {
  await redis.setex(`session:${sessionId}`, 86400, JSON.stringify(data)); // 24 hours
}

export async function getSession(sessionId: string) {
  const data = await redis.get(`session:${sessionId}`);
  return data ? JSON.parse(data as string) : null;
}
```

## AI Services

### Google Generative AI Integration
```typescript
// ai.ts - Gemini AI for content generation
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY!);

export const geminiAI = genAI.getGenerativeModel({ 
  model: 'gemini-1.5-flash',
  generationConfig: {
    temperature: 0.7,
    topP: 0.8,
    topK: 40,
    maxOutputTokens: 2048,
  }
});

// Generate study plan recommendations
export async function generateStudyPlan(userProgress: UserProgress): Promise<StudyPlan> {
  const prompt = `
    Based on this SAT student's performance data, generate a personalized study plan:
    
    Performance Summary:
    - Total Score: ${userProgress.totalScore}/1600
    - Math Score: ${userProgress.mathScore}/800  
    - Verbal Score: ${userProgress.verbalScore}/800
    - Weak Areas: ${userProgress.weakAreas.join(', ')}
    - Time Available: ${userProgress.studyTimePerWeek} hours/week
    
    Generate a structured study plan with:
    1. Priority areas to focus on
    2. Specific skill recommendations
    3. Time allocation suggestions
    4. Practice question targets
    
    Format as JSON with priorities, skills, and time estimates.
  `;
  
  try {
    const result = await geminiAI.generateContent(prompt);
    const text = result.response.text();
    
    // Parse AI response into structured format
    return JSON.parse(text);
  } catch (error) {
    console.error('AI study plan generation failed:', error);
    throw new Error('Failed to generate study plan');
  }
}

// Generate vocabulary explanations
export async function generateVocabExplanation(word: string, context: string): Promise<VocabExplanation> {
  const prompt = `
    Explain the SAT vocabulary word "${word}" in context: "${context}"
    
    Provide:
    1. Definition appropriate for SAT level
    2. Example sentence using the word correctly
    3. Common synonyms
    4. Memory technique or mnemonic
    
    Keep explanations concise and student-friendly.
  `;
  
  const result = await geminiAI.generateContent(prompt);
  
  return {
    word,
    definition: result.response.text(),
    context,
    generatedAt: new Date()
  };
}
```

## API Route Patterns

### Server-side Route Implementation
```typescript
// Example API route using all services
// routes/api/test/[testId]/+server.ts

import { json, error } from '@sveltejs/kit';
import { 
  adminAuth, 
  supabase, 
  redis, 
  client as contentful 
} from '$lib/server';

export async function GET({ params, request, locals }) {
  const { testId } = params;
  const user = locals.user;
  
  // Authentication check
  if (!user) {
    throw error(401, 'Authentication required');
  }
  
  // Rate limiting
  const withinLimit = await checkRateLimit(user.uid, 'test_fetch');
  if (!withinLimit) {
    throw error(429, 'Rate limit exceeded');
  }
  
  try {
    // Check cache first
    const cacheKey = `test:${testId}`;
    const cached = await redis.get(cacheKey);
    
    if (cached) {
      return json(JSON.parse(cached as string));
    }
    
    // Fetch from Supabase
    const testData = await getTestTemplate(testId);
    
    // Cache for 1 hour
    await redis.setex(cacheKey, 3600, JSON.stringify(testData));
    
    return json(testData);
    
  } catch (err) {
    console.error('Test fetch failed:', err);
    throw error(500, 'Failed to fetch test data');
  }
}

export async function POST({ request, locals }) {
  const user = locals.user;
  
  if (!user) {
    throw error(401, 'Authentication required');
  }
  
  const { answers, timeSpent } = await request.json();
  
  // Process and save test results
  const results = await processTestResults(answers, timeSpent);
  const testResultId = await saveTestResults(user.uid, results);
  
  // Clear user's question cache
  await redis.del(`questions:${user.uid}:*`);
  
  return json({ 
    success: true, 
    resultId: testResultId,
    score: results.totalScore 
  });
}
```

## Environment Configuration

### Required Environment Variables
```bash
# Firebase Admin SDK
FIREBASE_PROJECT_ID=dsat16-17663
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"

# Supabase
SUPABASE_URL=https://xxx.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJ...

# Contentful CMS
CONTENTFUL_SPACE_ID=xxx
CONTENTFUL_ACCESS_TOKEN=xxx
CONTENTFUL_ENVIRONMENT=master

# Upstash Redis
UPSTASH_REDIS_REST_URL=https://xxx.upstash.io
UPSTASH_REDIS_REST_TOKEN=xxx

# Google AI
GOOGLE_AI_API_KEY=AIza...

# Stripe (if needed)
STRIPE_SECRET_KEY=sk_live_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

## Error Handling

### Centralized Error Management
```typescript
// Error handling utilities
export class ServiceError extends Error {
  constructor(
    message: string,
    public service: string,
    public statusCode: number = 500
  ) {
    super(message);
    this.name = 'ServiceError';
  }
}

// Service wrapper with error handling
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  serviceName: string
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    console.error(`${serviceName} error:`, error);
    
    if (error instanceof ServiceError) {
      throw error;
    }
    
    throw new ServiceError(
      `${serviceName} operation failed`,
      serviceName,
      500
    );
  }
}
```

## Development Tips

1. **Environment**: Use different environments for development/staging/production
2. **Caching**: Implement cache invalidation strategies for real-time data
3. **Monitoring**: Set up logging and monitoring for all service calls
4. **Security**: Validate all inputs and sanitize data before database operations
5. **Performance**: Use connection pooling and optimize database queries
6. **Testing**: Mock external services for unit tests
7. **Deployment**: Configure services for serverless environments (Vercel, Netlify)

## Regional Configuration

All services are configured for **asia-southeast1** region for optimal performance:
- Firebase Firestore: asia-southeast1
- Supabase: Southeast Asia region
- Redis: Asia Pacific region
- Content delivery optimized for Asian users