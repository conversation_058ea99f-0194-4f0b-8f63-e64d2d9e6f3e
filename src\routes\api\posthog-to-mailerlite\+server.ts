import { POSTHOG_API_KEY } from '$env/static/private';
import { mailerlite } from '$lib/server';
import type { RequestHandler } from './$types';

interface Payload {
    email: string;
    countryName: string;
    browserLanguage: string;
    displayName: string;
}

export const POST: RequestHandler = async ({ request }) => {
    // Extract Key from Header
    const key = request.headers.get('key');

    if (key !== POSTHOG_API_KEY) return new Response('Unauthorized', { status: 401 });

    const { email, countryName, browserLanguage, displayName }: Payload = await request.json();

    if (!email || !countryName || !browserLanguage || !displayName) return new Response('Bad Request', { status: 400 });

    // Create subscriber
    await mailerlite.subscribers.createOrUpdate({
        email,
        fields: {
            name: displayName,
            country: countryName,
            browser_language: browserLanguage,
        },
        groups: ["162112583478805559"],
        status: "active",
    })

    console.log("Subscriber created: ", email)

    return new Response("OK", { status: 200 });
};