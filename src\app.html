<!DOCTYPE html>

<html lang="en">
	<head>
		<meta name="viewport" content="width=device-width, initial-scale=1" />

		<!-- social sharing image -->
		<meta property="og:title" content="DSAT16"  />
		<meta property="og:description" content="SAT Test Prep Platform aimed at gamifying the SAT experience." />
		<meta property="og:type" content="website" />
		<!-- <meta property="og:url" content="https://www.dsat16.com" /> -->
		<meta property="og:image" content="/social_sharing_image.png" />
		<meta name="twitter:card" content="summary_large_image">

		<meta charset="utf-8" />
		<link rel="icon" href="%sveltekit.assets%/favicon.ico" />
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link defer crossorigin="anonymous" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;700&display=swap&subset=latin" rel="stylesheet">
		<link defer crossorigin="anonymous" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap&subset=latin" rel="stylesheet">
		<link defer crossorigin="anonymous" href='https://fonts.googleapis.com/css?family=Merriweather&display=swap' rel='stylesheet'>
		<link defer crossorigin="anonymous" href="https://fonts.googleapis.com/css2?family=Chivo&display=swap&subset=latin" rel="stylesheet">
		%sveltekit.head%
	</head>
	<body data-sveltekit-preload-data="hover">
		<div style="display: contents">%sveltekit.body%</div>
	</body>     
</html>

<style>
	:root {
		--sky-blue: #66E2FF;
		--purple: #8C53FF;
		--aquamarine: #55ECB2;
		--yellow: #FFC800;
		--tangerine: #F68712;
		--rose: #EB47AB;
		--pitch-black: #000000;
		--very-light-sky-blue: #f5fdff;
		--light-sky-blue: #DAF9FF;
		--light-purple: #EEE5FF;
		--light-aquamarine: #D6FAED;
		--light-yellow: #FFF1C1;
		--light-tangerine: #FDE2C5;
		--light-rose: #FBDEF0;
		--white: #FFFFFF;
		--charcoal: #333333;
		--green: #008000;
	}

    html {
        scroll-behavior: smooth;
    }

	* {
		padding: 0;
		margin: 0;
		box-sizing: border-box;
	}

	button:hover:enabled, a:hover:enabled {
		cursor: pointer;
	}

	button {
		background: none;
		border: none;
	}

	a {
		color: black;
		text-decoration: none;
	}

	.anno-highlight {
        font-family: "Merriweather";
		cursor: pointer;
		position: relative;
		user-select: none;
    }

	.anno-desc, .tmp-anno-desc {
		position: absolute;
		top: 20px;
		width: max-content;
		left: 0;
		z-index: 1;
		padding: 6px 10px;
		border-radius: 10px;
		border: 2px solid #000;
		background: #FFF;
		color: #000;
		font-family: 'Inter';
		font-size: 17px;
		font-style: normal;
		font-weight: 500;
		line-height: normal;
		transition: opacity 0.15s, visibility 0.15s;
		opacity: 0;
		visibility: hidden;
	}

	.anno-desc {
		position: fixed;
	}

	.tmp-anno-desc {
		z-index: 2;
		visibility: visible;
		opacity: 1;
	}

	.anno-highlight:hover .anno-desc {
		visibility: visible;
		opacity: 1;
	}

	.link {
		text-decoration: underline #FF66C4;
		color: #FF66C4;
	}

	.grecaptcha-badge { 
		visibility: hidden;
	}

	@keyframes fade-in {
        from {
            opacity: 0;
        }
    }

    @keyframes fade-out {
        to {
            opacity: 0;
        }
    }

    @keyframes slide-from-right {
        from {
            transform: translateX(30px);
        }
    }

    @keyframes slide-to-left {
        to {
            transform: translateX(-30px);
        }
    }

    :root::view-transition-old(root) {
        animation:
            90ms cubic-bezier(0.4, 0, 1, 1) both fade-out,
            300ms cubic-bezier(0.4, 0, 0.2, 1) both slide-to-left;
    }

    :root::view-transition-new(root) {
        animation:
            210ms cubic-bezier(0, 0, 0.2, 1) 90ms both fade-in,
            300ms cubic-bezier(0.4, 0, 0.2, 1) both slide-from-right;
    }
</style>
