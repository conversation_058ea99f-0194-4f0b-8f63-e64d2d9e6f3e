# dashboard/

User dashboard widgets displaying progress, achievements, and study metrics.

## Progress Tracking
- **QuestionBankProgress.svelte** - Progress indicator for question bank completion
- **VocabProgress.svelte** - Vocabulary learning progress and statistics
- **EstimatedScore.svelte** - Current SAT score estimation based on performance

## Gamification
- **DailyMissions.svelte** - Daily task/challenge system with mission tracking
- **StreakBadge.svelte** - Streak counter and achievement badges for consecutive study days

## Goals & Motivation  
- **UniAim.svelte** - University target setting and progress toward admission goals
- **ScoreDescription.svelte** - Score interpretation and improvement suggestions

**Integration**: 
- Connects to missions system for gamification features
- Pulls data from Firestore for user progress
- Uses PostHog for tracking milestone achievements
- Updates real-time with Svelte 5 reactivity patterns

**No index.ts**: Components imported directly where needed