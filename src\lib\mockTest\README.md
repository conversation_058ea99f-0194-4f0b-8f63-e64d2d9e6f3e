# Mock Test System

Comprehensive SAT simulation engine with adaptive testing, real-time scoring, and detailed walkthrough capabilities.

## Overview

The mock test system is the core testing engine that provides realistic SAT practice experiences. It features adaptive difficulty, section timing, annotation support, and comprehensive solution walkthroughs. The system integrates with missions, analytics, and generates detailed analysis reports.

## Architecture

```
mockTest/
├── simulation/              # Test execution engine
│   ├── index.js            # Simulation component exports
│   ├── SimTop.svelte       # Main test controller and header
│   ├── SimMid.svelte       # Question display area
│   ├── SimBottom.svelte    # Navigation and controls
│   ├── SimReview.svelte    # Review and navigation panel
│   ├── SimRef.svelte       # Reference sheet overlay
│   ├── SimCalc.svelte      # Built-in calculator
│   ├── Annotate.svelte     # Text annotation interface
│   ├── MinitestIntro.svelte # Minitest introduction
│   ├── SimBreaking.svelte  # Break time countdown
│   └── Submitting.svelte   # Test submission processing
├── ui/                     # Question display components
│   ├── Question.svelte     # Main question router
│   ├── Choices.svelte      # Multiple choice interface
│   ├── AnswerBox.svelte    # Answer input handling
│   ├── Passage.svelte      # Reading passage display
│   ├── Graph.svelte        # Mathematical chart rendering
│   ├── MidMath.svelte      # Math section layout
│   ├── MidVerbal.svelte    # Verbal section layout
│   ├── MidSPR.svelte       # Student-Produced Response
│   ├── SimClock.svelte     # Timer and countdown
│   ├── MarkBar.svelte      # Question navigation bar
│   ├── TwoSide.svelte      # Split-screen layout
│   └── Walk*.svelte        # Walkthrough components
└── walkthrough/            # Solution review system
    ├── index.js            # Walkthrough exports
    ├── WalkTop.svelte      # Review header
    ├── WalkMid.svelte      # Solution display
    ├── WalkBottom.svelte   # Review navigation
    └── WalkReview.svelte   # Review summary
```

## Core Test Flow

### Test Execution Lifecycle
```svelte
<!-- SimTop.svelte - Main controller -->
<script>
  import { finalAnswers, finalMarked } from '$lib/stores';
  import { incrementMissionProgress } from '$lib/missions';
  
  let currentSection = $state(0);
  let currentQuestionId = $state(0);
  let currentQuestionIndex = $state(0);
  let breakTime = $state(false);
  let finishedTest = $state(false);
  
  // Section timing
  let totalSeconds = $state(0);
  let sectionTotalSeconds = $state(0);
  
  // Test completion
  async function submitTest() {
    submitLoading = true;
    
    // Process answers and calculate score
    const results = await processTestResults($finalAnswers, testData);
    
    // Update mission progress
    await incrementMissionProgress($user?.uid, 'mock_test_completed', 1);
    
    // Navigate to analysis
    goto(`/study/analysis/${results.id}`);
  }
</script>
```

### Question Display System
```svelte
<!-- Question.svelte - Question type router -->
<script>
  import MidMath from './MidMath.svelte';
  import MidVerbal from './MidVerbal.svelte';
  
  let { currentSection, question, currentQuestionIndex, testData } = $props();
  
  let isVerbal = $derived(
    question.question_type === 'Reading and Writing' || 
    question.domain === 'Reading and Writing'
  );
  
  let currentAnswer = $derived($finalAnswers[currentQuestionId] ?? '');
  
  function updateAnswer(newAnswer) {
    $finalAnswers[currentQuestionId] = newAnswer;
  }
</script>

{#if isVerbal}
  <MidVerbal 
    {question}
    answer={currentAnswer}
    onAnswerChange={updateAnswer}
  />
{:else}
  <MidMath 
    {question}
    answer={currentAnswer}  
    onAnswerChange={updateAnswer}
  />
{/if}
```

## Question Types & Layouts

### Math Section Components
```svelte
<!-- MidMath.svelte - Math question layout -->
<script>
  import { Graph, AnswerBox, Choices } from '../ui';
  
  let { question, answer, onAnswerChange } = $props();
  
  let hasGraph = $derived(question.graph_data);
  let isGridIn = $derived(question.question_format === 'spr'); // Student-Produced Response
</script>

<div class="math-question">
  <div class="question-content">
    <p>{@html question.question_text}</p>
    
    {#if hasGraph}
      <Graph data={question.graph_data} />
    {/if}
  </div>
  
  <div class="answer-area">
    {#if isGridIn}
      <AnswerBox 
        value={answer}
        onChange={onAnswerChange}
        type="numeric"
      />
    {:else}
      <Choices 
        options={question.choices}
        selected={answer}
        onSelect={onAnswerChange}
      />
    {/if}
  </div>
</div>
```

### Verbal Section Components  
```svelte
<!-- MidVerbal.svelte - Reading and Writing layout -->
<script>
  import { Passage, TwoSide } from '../ui';
  
  let { question, answer, onAnswerChange } = $props();
  
  let hasPassage = $derived(question.passage_text);
</script>

{#if hasPassage}
  <TwoSide>
    <div slot="left">
      <Passage 
        text={question.passage_text}
        title={question.passage_title}
        annotationEnabled={true}
      />
    </div>
    
    <div slot="right">
      <div class="question-panel">
        <p>{@html question.question_text}</p>
        
        <Choices 
          options={question.choices}
          selected={answer}
          onSelect={onAnswerChange}
        />
      </div>
    </div>
  </TwoSide>
{:else}
  <!-- Single-column layout for non-passage questions -->
  <div class="verbal-question">
    <p>{@html question.question_text}</p>
    <Choices 
      options={question.choices}
      selected={answer}
      onSelect={onAnswerChange}
    />
  </div>
{/if}
```

## Timer & Section Management

### Clock System
```svelte
<!-- SimClock.svelte - Test timer -->
<script>
  let { totalSeconds, sectionTimeLimit, onTimeUp } = $props();
  
  let timeRemaining = $derived(sectionTimeLimit - totalSeconds);
  let minutes = $derived(Math.floor(timeRemaining / 60));
  let seconds = $derived(timeRemaining % 60);
  
  let isWarning = $derived(timeRemaining < 300); // Last 5 minutes
  let isCritical = $derived(timeRemaining < 60);  // Last minute
  
  $effect(() => {
    if (timeRemaining <= 0) {
      onTimeUp();
    }
  });
</script>

<div class="clock" class:warning={isWarning} class:critical={isCritical}>
  <span class="time">
    {minutes.toString().padStart(2, '0')}:{seconds.toString().padStart(2, '0')}
  </span>
  
  {#if isWarning}
    <span class="alert">⚠️ Time Running Out</span>
  {/if}
</div>
```

### Section Breaks
```svelte
<!-- SimBreaking.svelte - Break time management -->
<script>
  let { breakDuration = 600, onBreakEnd } = $props(); // 10 minutes
  
  let breakTimeLeft = $state(breakDuration);
  let breakTimer;
  
  onMount(() => {
    breakTimer = setInterval(() => {
      breakTimeLeft--;
      if (breakTimeLeft <= 0) {
        clearInterval(breakTimer);
        onBreakEnd();
      }
    }, 1000);
    
    return () => clearInterval(breakTimer);
  });
</script>

<div class="break-screen">
  <h2>Break Time</h2>
  <p>You have {Math.floor(breakTimeLeft / 60)} minutes remaining</p>
  <p>Stretch, hydrate, and prepare for the next section!</p>
  
  <button onclick={onBreakEnd}>
    End Break Early
  </button>
</div>
```

## Navigation & Review

### Question Navigation Bar
```svelte
<!-- MarkBar.svelte - Question navigation -->
<script>
  import { finalAnswers, finalMarked } from '$lib/stores';
  
  let { sections, currentSection, currentQuestionIndex, onQuestionSelect } = $props();
  
  let currentSectionQuestions = $derived(sections[currentSection]);
</script>

<div class="mark-bar">
  {#each currentSectionQuestions as question, index}
    <button 
      class="question-marker"
      class:current={index === currentQuestionIndex}
      class:answered={$finalAnswers[question.id]}
      class:marked={$finalMarked[question.id]}
      onclick={() => onQuestionSelect(index)}
    >
      {index + 1}
    </button>
  {/each}
</div>

<style>
  .question-marker {
    @apply w-8 h-8 rounded border-2 border-gray-300 bg-white;
  }
  
  .question-marker.current {
    @apply border-blue-500 bg-blue-100;
  }
  
  .question-marker.answered {
    @apply bg-green-100 border-green-500;
  }
  
  .question-marker.marked {
    @apply bg-yellow-100 border-yellow-500;
  }
</style>
```

## Tools & Features

### Built-in Calculator
```svelte
<!-- SimCalc.svelte - Calculator overlay -->
<script>
  let display = $state('0');
  let operation = $state(null);
  let previousValue = $state(null);
  let waitingForNewValue = $state(false);
  
  function inputNumber(num) {
    if (waitingForNewValue) {
      display = String(num);
      waitingForNewValue = false;
    } else {
      display = display === '0' ? String(num) : display + num;
    }
  }
  
  function inputOperation(nextOperation) {
    const inputValue = parseFloat(display);
    
    if (previousValue === null) {
      previousValue = inputValue;
    } else if (operation) {
      const currentValue = previousValue || 0;
      const newValue = calculate(currentValue, inputValue, operation);
      
      display = String(newValue);
      previousValue = newValue;
    }
    
    waitingForNewValue = true;
    operation = nextOperation;
  }
  
  function calculate(firstValue, secondValue, operation) {
    switch (operation) {
      case '+': return firstValue + secondValue;
      case '-': return firstValue - secondValue;
      case '×': return firstValue * secondValue;
      case '÷': return firstValue / secondValue;
      default: return secondValue;
    }
  }
</script>

<div class="calculator">
  <div class="display">{display}</div>
  
  <div class="buttons">
    <button onclick={() => inputNumber(7)}>7</button>
    <button onclick={() => inputNumber(8)}>8</button>
    <button onclick={() => inputNumber(9)}>9</button>
    <button onclick={() => inputOperation('÷')}>÷</button>
    <!-- More buttons... -->
  </div>
</div>
```

### Text Annotation
```svelte
<!-- Annotate.svelte - Annotation interface -->
<script>
  import { annotateManager } from '$lib/annotate';
  
  let { passageId, userId } = $props();
  let annotations = $state([]);
  let selectedColor = $state('yellow');
  
  function handleTextSelection(event) {
    const selection = window.getSelection();
    if (selection.rangeCount > 0 && !selection.isCollapsed) {
      const highlight = annotateManager.createHighlight({
        range: selection.getRangeAt(0),
        color: selectedColor,
        passageId,
        userId
      });
      
      annotations = [...annotations, highlight];
    }
  }
</script>

<div class="annotation-tools">
  <div class="color-selector">
    {#each ['yellow', 'blue', 'green', 'pink'] as color}
      <button 
        class="color-btn {color}"
        class:selected={selectedColor === color}
        onclick={() => selectedColor = color}
      >
      </button>
    {/each}
  </div>
  
  <button onclick={() => annotations = []}>
    Clear All
  </button>
</div>
```

## Solution Walkthrough System

### Review Interface
```svelte
<!-- WalkMid.svelte - Solution display -->
<script>
  import { WalkSolution, WalkMath } from '../ui';
  
  let { question, userAnswer, correctAnswer, explanation } = $props();
  
  let isCorrect = $derived(userAnswer === correctAnswer);
  let isMath = $derived(question.domain === 'Math');
</script>

<div class="solution-review">
  <div class="answer-status" class:correct={isCorrect} class:incorrect={!isCorrect}>
    {#if isCorrect}
      <span class="status-icon">✅</span>
      <span>Correct!</span>
    {:else}
      <span class="status-icon">❌</span>
      <span>Incorrect</span>
      <span class="correct-answer">Correct answer: {correctAnswer}</span>
    {/if}
  </div>
  
  <div class="explanation">
    {#if isMath && question.solution_steps}
      <WalkMath steps={question.solution_steps} />
    {:else}
      <WalkSolution explanation={explanation} />
    {/if}
  </div>
</div>
```

## Data Integration

### Test Data Structure
```typescript
interface TestData {
  id: string;
  name: string;
  type: 'full' | 'minitest' | 'practice';
  sections: Section[];
  timeLimit: number; // Total test time in seconds
  sectionTimeLimits: number[]; // Time per section
}

interface Section {
  id: string;
  name: string; // "Math Module 1", "Reading and Writing Module 1"
  questions: Question[];
  timeLimit: number;
}

interface Question {
  id: string;
  question_text: string;
  question_type: 'Reading and Writing' | 'Math';
  domain: string;
  skill: string;
  difficulty: number; // 1-5
  choices?: string[]; // For multiple choice
  correct_answer: string;
  explanation: string;
  solution_steps?: string[]; // For math problems
  passage_text?: string; // For reading questions
  graph_data?: any; // For math with graphs
}
```

### Answer Storage
```typescript
// Global stores for test state
import { finalAnswers, finalMarked } from '$lib/stores';

// Answer format: { questionId: selectedAnswer }
$finalAnswers = {
  'q1': 'A',
  'q2': 'C', 
  'q3': '15', // Grid-in answer
};

// Marked questions for review
$finalMarked = {
  'q5': true,
  'q12': true
};
```

## Analytics & Scoring

### Performance Tracking
```typescript
// Test completion analytics
import posthog from 'posthog-js';

const trackTestCompletion = (testResults) => {
  posthog.capture('mock_test_completed', {
    test_type: testData.type,
    total_score: testResults.totalScore,
    math_score: testResults.mathScore,
    verbal_score: testResults.verbalScore,
    time_spent: testResults.timeSpent,
    questions_correct: testResults.questionsCorrect,
    questions_total: testResults.questionsTotal
  });
};
```

## Development Tips

1. **State Management**: Use Svelte 5 runes for reactive test state
2. **Performance**: Lazy load question content and images
3. **Accessibility**: Ensure timer announcements and keyboard navigation
4. **Mobile Support**: Test touch interactions and responsive layouts
5. **Error Handling**: Graceful degradation for network issues
6. **Testing**: Mock test data and timer functions for development
7. **Security**: Validate all answers server-side before scoring