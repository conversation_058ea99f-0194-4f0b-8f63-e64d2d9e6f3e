# questionBank/

Individual question practice interface for targeted skill development.

## Components
- **QuestionBank.svelte** - Main question practice interface with filtering, progress tracking, and performance analytics

**Features**:
- Question filtering by difficulty, type, and module
- Progress tracking and performance metrics
- Integration with missions system for daily practice goals
- Detailed answer explanations and solutions
- Adaptive question selection based on user performance

**Data Source**: Supabase question database
**Routes**: Used in /study/question-bank page
**Integration**: Connects to missions system for progress tracking and gamification

**No index.ts**: Single component imported directly