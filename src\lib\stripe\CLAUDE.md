# stripe/

Payment processing integration for subscription management and billing.

## Components
- **stripe.ts** - Stripe SDK integration for payment processing, subscription management, and webhook handling

**Features**:
- Subscription plan management ("Pro", "Bootcamp", "Pro+Bootcamp" roles)  
- Secure payment processing and billing cycles
- Webhook handling for subscription events (payment success, cancellation, etc.)
- Integration with Firebase Auth custom claims for role updates

**Security**: 
- Server-side webhook verification for payment events
- Role-based access control tied to subscription status
- Secure handling of payment data and customer information

**Routes**: Used in /api/stripe/webhook endpoint and pricing pages
**Integration**: Updates Firebase user roles based on subscription status

**No index.ts**: Single service file imported directly where needed