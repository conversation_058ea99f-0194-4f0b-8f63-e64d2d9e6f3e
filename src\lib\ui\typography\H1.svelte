<script>
    /** @type {{children?: import('svelte').Snippet}} */
    let { children } = $props();
</script>

<!-- 
    @component
    A styled h1 element.
    
    Usage:
    ```tsx
    <H1>Heading 1</H1>
    ```
-->

<h1>
    {@render children?.()}
</h1>

<style>
    h1 {
        font-family: "Inter";
        font-size: 3.625rem;
        font-weight: 600;
        text-align: var(--text-align, inherit);
    }

    @media (max-width: 540px) {
        h1 {
            font-size: 2.25rem;
        }
    }
</style>
