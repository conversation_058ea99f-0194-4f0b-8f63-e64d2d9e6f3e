<script>
    import { P2 } from '$lib/ui';
    
    let { 
        text = "Medium length banner heading",
        linkText = "with link", 
        linkHref = "#" 
    } = $props();
</script>

<div class="banner">
    <div class="banner-content">
        <P2>{text} <a href={linkHref} class="banner-link">{linkText}</a> goes here</P2>
        <button class="banner-close" aria-label="Close banner">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
        </button>
    </div>
</div>

<style>
    /* Banner */
    .banner {
        background: var(--rose);
        border-bottom: 0.25rem solid var(--pitch-black);
        padding: 1rem 2rem;
    }
    
    .banner-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        max-width: 90rem;
        margin: 0 auto;
        gap: 1rem;
    }
    
    .banner-link {
        color: var(--pitch-black);
        text-decoration: underline;
        font-weight: 700;
    }
    
    .banner-link:hover {
        background: var(--yellow);
        padding: 0.25rem;
    }
    
    .banner-close {
        background: var(--pitch-black);
        color: white;
        border: none;
        cursor: pointer;
        padding: 0.5rem;
    }
    
    .banner-close:hover {
        background: var(--yellow);
        color: var(--pitch-black);
    }
    
    /* Mobile Responsiveness */
    @media (max-width: 48rem) {
        .banner-content {
            padding: 0 1rem;
        }
    }
</style>
