# Library Documentation

This directory contains the complete component library and business logic for the DSAT16 SAT preparation platform. Each module is self-contained with comprehensive documentation to help developers understand and extend the system.

## Module Overview

### Core Features

#### **[analysis/](./analysis/README.md)** - Post-test Analysis & Visualization
Comprehensive test analysis system with AI-powered insights and score predictions. Components include circular progress bars, skill assessment charts, score comparisons, and personalized study plan generation. Handles detailed performance visualization and sharing capabilities.

#### **[mockTest/](./mockTest/README.md)** - SAT Simulation Engine  
Complete adaptive testing system with real-time difficulty adjustment, section timing, and calculator integration. Includes simulation components, UI elements for different question types, and walkthrough functionality. Supports multi-section tests with proper state management.

#### **[questionBank/](./questionBank/README.md)** - Individual Question Practice
Standalone question practice system with intelligent filtering, difficulty selection, and progress tracking. Provides focused practice outside of full test simulations with detailed explanations and performance analytics.

#### **[vocabTool/](./vocabTool/README.md)** - Spaced Repetition Learning
Scientific vocabulary learning system using ts-fsrs algorithm for optimal retention. Features AI-generated explanations, adaptive difficulty adjustment, and personalized review scheduling. Includes word cards and response buttons for efficient study sessions.

#### **[bootcamp/](./bootcamp/README.md)** - Structured Course Content
Educational content management system with Contentful CMS integration. Provides structured lessons, interactive notes, navigation components, and additional resources. Includes hyperlinked note system and content organization for comprehensive SAT preparation.

### <� Gamification & Progress

#### **[missions/](./missions/README.md)** - Daily Challenge System
Gamification engine with daily missions, streak tracking, and PostHog analytics integration. Manages mission catalogs, progress tracking, and reward systems. Includes store management for mission state and completion tracking.

#### **[dashboard/](./dashboard/README.md)** - User Progress Widgets
Dashboard components showing user progress, daily missions, estimated scores, and motivational elements. Features streak badges, university aim tracking, vocabulary progress, and question bank completion visualization.

### Annotation

#### **[annotate/](./annotate/README.md)** - Text Annotation System
Text annotation manager for passages during test-taking. Handles highlighting, note-taking, and markup functionality for reading comprehension questions. Provides tools for students to interact with test passages effectively. This is used in Question Bank and Simulation.

### Infrastructure

#### **[firebase/](./firebase/README.md)** - Authentication & Database
Firebase integration layer handling authentication, Firestore database operations, and real-time data synchronization. Includes user management, role-based access control, and vocabulary database operations with Svelte 5 reactive patterns.

#### **[server/](./server/README.md)** - Backend Service Integrations
Server-side service layer integrating Supabase, Contentful CMS, Redis caching, Google AI, and administrative functions. Provides centralized backend operations and external service management.

#### **[stripe/](./stripe/README.md)** - Payment Processing
Stripe integration for subscription management and payment processing. Handles checkout flows, webhook processing, subscription lifecycle management, and billing operations.

#### **[types/](./types/README.md)** - TypeScript Definitions
Comprehensive type system covering questions, missions, vocabulary, user data, and database schemas. Provides type safety across the entire application with auto-generated Supabase types.

### User Interface

#### **[ui/](./ui/README.md)** - Reusable Component Library
Complete UI component system with buttons, forms, typography, modals, and specialized educational components. Includes pricing cards, social icons, and accessibility-focused design patterns with Tailwind CSS integration.

#### **[landingPage/](./landingPage/README.md)** - Marketing Website Components
Marketing website components optimized for conversion, including hero sections, feature comparisons, social proof, and call-to-action elements. Designed for landing page optimization and user acquisition.

#### **[study/](./study/README.md)** - Study Area Navigation
Navigation component for the protected study area, providing consistent layout and navigation patterns across study tools and features.

### Orphan Files & Utilities

#### **stores.js** - Global State Management
Global Svelte stores for managing test state across the application:
- `finalAnswers` - User's answers during test sessions
- `finalMarked` - Questions marked for review during tests
- Provides centralized state management for test-taking functionality

#### **utilities.ts** - Helper Functions
Collection of utility functions used throughout the application:
- `debounce` - Input debouncing utility for performance optimization
- Additional helper functions for common operations
- Shared utilities to reduce code duplication

#### **[assets/](./assets/)** - Static Visual Content
Static image assets organized by feature area:
- `home/` - Landing page images (dashboard.png, hero.png, qb.png, simulation.png, vocab-tool.png)
- `phuc.png` - User avatar/profile image
- `step-1.png`, `step-2.png` - Process step illustrations
- Visual content supporting marketing and UI components

## Quick Start

### Import Patterns
```typescript
// Import from specific modules
import { Analysis } from '$lib/analysis';
import { QuestionBank } from '$lib/questionBank';
import { Button, H1, P1 } from '$lib/ui';

// Import services
import { auth, user, db } from '$lib/firebase';
import { supabase, geminiAI } from '$lib/server';

// Import types
import type { Question, TestResult } from '$lib/types';

// Import utilities and stores
import { debounce } from '$lib/utilities';
import { finalAnswers, finalMarked } from '$lib/stores';
```

### Architecture Highlights

**=% Svelte 5 Features**
- Modern reactivity with `$state`, `$derived`, `$effect` runes
- Type-safe component props and event handling
- Optimized rendering and state management

**= Frontend Authentication Flow**
```
Client Auth (firebase/auth.svelte.ts) 
    �
Server Verification (server/admin.ts)
    �
Role-based Access Control
    � 
Protected Routes & Features
```



**=� Data Flow**
```
User Actions � Firebase/Supabase � Real-time Updates � UI Components
                      �
                PostHog Analytics
                      �
               Mission Progress Updates
```

## <� Key Integration Points

### Mission System Integration
A few user actions integrate with the gamification system:
```typescript
import { incrementMissionProgress } from '$lib/missions';

// After completing actions
await incrementMissionProgress(userId, 'questions_answered', 1);
await incrementMissionProgress(userId, 'mock_test_completed', 1);
```

### Real-time Data Sync
Firebase powers real-time updates across the platform:
```typescript
import { onSnapshot } from 'firebase/firestore';
import { db } from '$lib/firebase';

// Subscribe to user progress updates
const unsubscribe = onSnapshot(doc(db, 'users', userId), (doc) => {
  // Update UI reactively
  userData = doc.data();
});
```

### AI-Powered Features
Google Generative AI enhances the learning experience:
```typescript
import { geminiAI } from '$lib/server';

// Generate personalized study plans
const studyPlan = await geminiAI.generateContent(promptTemplate);

// Create vocabulary explanations
const explanation = await generateVocabExplanation(word, context);
```

### Global State Management
Svelte stores provide centralized state for test sessions:
```typescript
import { finalAnswers, finalMarked } from '$lib/stores';

// Update test answers
finalAnswers.update(answers => ({
  ...answers,
  [questionId]: selectedAnswer
}));

// Mark questions for review
finalMarked.update(marked => ({
  ...marked,
  [questionId]: true
}));
```

## =� Development Workflow

### 1. Understanding Components
Each module includes:
- **README.md** - Comprehensive usage guide with examples
- **CLAUDE.md** - Concise technical reference for AI assistance
- **index.ts** - Clean export patterns and public API

### 2. Adding New Features
1. Check existing patterns in relevant modules
2. Follow TypeScript interfaces from `types/`
3. Integrate with missions system when appropriate
4. Update analytics tracking with PostHog
5. Ensure mobile responsiveness
6. Update global stores if needed for state management

### 3. Testing Strategy
```typescript
// Mock external services
vi.mock('$lib/firebase', () => ({
  auth: mockAuth,
  db: mockFirestore
}));

// Mock global stores
vi.mock('$lib/stores', () => ({
  finalAnswers: mockStore({}),
  finalMarked: mockStore({})
}));

// Test component behavior
import { render, fireEvent } from '@testing-library/svelte';
import Component from '$lib/ui/Button.svelte';
```

## <� Design System

### Color Palette
```css
:root {
  --primary: #6366f1;      /* Indigo */
  --secondary: #8b5cf6;    /* Purple */
  --tangerine: #fb923c;    /* Orange accent */
  --rose: #f43f5e;         /* Pink accent */
  --aquamarine: #06d6a0;   /* Teal accent */
  --charcoal: #374151;     /* Dark gray */
  --sky-blue: #0ea5e9;     /* Primary button color */
  --pitch-black: #000000;  /* Border and text color */
}
```

### Component Standards
- **Accessibility**: ARIA labels, keyboard navigation
- **Responsive**: Mobile-first design with Tailwind
- **Type Safety**: Full TypeScript coverage
- **Performance**: Lazy loading and code splitting
- **State Management**: Integrate with global stores when needed

## =� Analytics & Monitoring

### PostHog Integration
Track user engagement and feature usage:
```typescript
import posthog from 'posthog-js';

posthog.capture('feature_used', {
  feature_name: 'mock_test_started',
  user_plan: userPlan,
  test_type: testType
});
```

### Performance Monitoring
- Firebase Performance Monitoring
- Real-time error tracking
- User behavior analytics
- Conversion funnel analysis

## =� Learning Resources

### New Developer Onboarding
1. **Start with**: `ui/README.md` - Learn component patterns and design system
2. **Then explore**: `firebase/README.md` - Understand data flow and authentication
3. **Build features**: `missions/README.md` - See gamification integration
4. **Advanced topics**: `server/README.md` - Backend architecture and service integrations
5. **State management**: Review `stores.js` and `utilities.ts` for shared functionality

### Common Patterns
```typescript
// Reactive component with loading states
let loading = $state(false);
let data = $state(null);
let error = $state(null);

// Effects for data fetching
$effect(() => {
  if ($user?.uid) {
    fetchUserData($user.uid);
  }
});

// Event handlers with error handling
async function handleSubmit() {
  loading = true;
  try {
    await submitData();
    // Update global stores if needed
    finalAnswers.update(answers => ({ ...answers, newData }));
    // Success feedback
  } catch (err) {
    error = err.message;
  } finally {
    loading = false;
  }
}
```

## > Contributing Guidelines

### Code Standards
- Follow existing TypeScript patterns
- Use Svelte 5 runes for reactivity
- Maintain accessibility standards
- Write comprehensive tests
- Document new features in both README.md and CLAUDE.md

### Integration Requirements
- Connect with missions system for user actions
- Add PostHog tracking for analytics
- Ensure mobile responsiveness
- Follow design system patterns
- Update type definitions
- Consider global state management needs

### File Organization
- Place reusable components in appropriate module folders
- Use orphan files (`stores.js`, `utilities.ts`) for shared functionality
- Add static assets to `assets/` with logical organization
- Maintain clean export patterns in `index.ts` files

---

*This library powers the complete DSAT16 SAT preparation platform, from user authentication to AI-powered study recommendations. Each module is designed for maintainability, scalability, and developer productivity, with orphan files providing essential shared functionality.*