<script lang="ts">
    import { H2, H5, P1 } from "$lib/ui";
    import type { Card } from "$lib/types";
	import { browser } from "$app/environment";
    import DOMPurify from 'dompurify';
    
    // Props: currentCard contains the vocabulary word and hints
    //       currentPhase determines which content to display (0: word, 1: hint1, 2: hint2, 3: answer)
    let { currentCard, currentPhase, hasUserAnswered }: { currentCard: Card, currentPhase: 0 | 1 | 2 | 3, hasUserAnswered: boolean } = $props();
    let temp = $state(false);

    
    // State for sanitized hints
    let safeHint1 = $derived<string>(browser ? DOMPurify.sanitize(currentCard.vocabCard.hint1, { ALLOWED_TAGS: ['mark', 'b', 'i', 'u'] }) : "");
    let safeHint2 = $derived<string>(browser ? DOMPurify.sanitize(currentCard.vocabCard.hint2, { ALLOWED_TAGS: ['mark', 'b', 'i', 'u'] }) : "");
</script>

{#if temp}
    <mark></mark>
{/if}

<div class="card-container">
    <div class="navigation-bar">
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13.9414 3.48535C14.5678 2.44135 15.9149 1.90351 17.1123 2.57324L17.1133 2.57227C17.3068 2.66902 17.5063 2.79256 17.6895 2.97559C17.8727 3.15884 17.9969 3.35909 18.0938 3.55273L21.3252 10.0146L28.3584 11.0029C29.0621 11.0374 29.6016 11.4608 29.916 11.9062C30.2279 12.3481 30.4179 12.9466 30.3271 13.5596C30.2999 14.0699 30.0512 14.6045 29.5449 14.9307L24.542 19.8125L25.7842 26.8936L25.7861 26.9014V26.9023C26.0111 28.2537 25.1052 29.4112 23.9287 29.6465L23.832 29.666H23.4658C23.351 29.666 23.142 29.6683 22.9414 29.6396C22.7876 29.6177 22.5166 29.5614 22.2598 29.3789L15.999 26.0635L9.66699 29.417L9.66602 29.415C8.46362 30.1022 7.1055 29.5635 6.47559 28.5137L6.45508 28.4814L6.43848 28.4473C6.18497 27.9403 6.19922 27.4407 6.19922 27.1992V27.1123L6.21484 27.0264L7.45605 19.9463L2.36816 14.9824L2.3584 14.9736L2.35938 14.9727C1.4742 14.0874 1.43866 12.7368 2.25 11.8125C2.60619 11.2361 3.20538 11.0267 3.7627 11.0039L10.7959 10.0166L13.8984 3.56641L13.918 3.52441L13.9414 3.48535Z" fill="var(--purple)" stroke="black" stroke-width="2"/>
        </svg>
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13.9414 3.48535C14.5678 2.44135 15.9149 1.90351 17.1123 2.57324L17.1133 2.57227C17.3068 2.66902 17.5063 2.79256 17.6895 2.97559C17.8727 3.15884 17.9969 3.35909 18.0938 3.55273L21.3252 10.0146L28.3584 11.0029C29.0621 11.0374 29.6016 11.4608 29.916 11.9062C30.2279 12.3481 30.4179 12.9466 30.3271 13.5596C30.2999 14.0699 30.0512 14.6045 29.5449 14.9307L24.542 19.8125L25.7842 26.8936L25.7861 26.9014V26.9023C26.0111 28.2537 25.1052 29.4112 23.9287 29.6465L23.832 29.666H23.4658C23.351 29.666 23.142 29.6683 22.9414 29.6396C22.7876 29.6177 22.5166 29.5614 22.2598 29.3789L15.999 26.0635L9.66699 29.417L9.66602 29.415C8.46362 30.1022 7.1055 29.5635 6.47559 28.5137L6.45508 28.4814L6.43848 28.4473C6.18497 27.9403 6.19922 27.4407 6.19922 27.1992V27.1123L6.21484 27.0264L7.45605 19.9463L2.36816 14.9824L2.3584 14.9736L2.35938 14.9727C1.4742 14.0874 1.43866 12.7368 2.25 11.8125C2.60619 11.2361 3.20538 11.0267 3.7627 11.0039L10.7959 10.0166L13.8984 3.56641L13.918 3.52441L13.9414 3.48535Z" fill="var(--rose)" stroke="black" stroke-width="2"/>
        </svg>
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M13.9414 3.48535C14.5678 2.44135 15.9149 1.90351 17.1123 2.57324L17.1133 2.57227C17.3068 2.66902 17.5063 2.79256 17.6895 2.97559C17.8727 3.15884 17.9969 3.35909 18.0938 3.55273L21.3252 10.0146L28.3584 11.0029C29.0621 11.0374 29.6016 11.4608 29.916 11.9062C30.2279 12.3481 30.4179 12.9466 30.3271 13.5596C30.2999 14.0699 30.0512 14.6045 29.5449 14.9307L24.542 19.8125L25.7842 26.8936L25.7861 26.9014V26.9023C26.0111 28.2537 25.1052 29.4112 23.9287 29.6465L23.832 29.666H23.4658C23.351 29.666 23.142 29.6683 22.9414 29.6396C22.7876 29.6177 22.5166 29.5614 22.2598 29.3789L15.999 26.0635L9.66699 29.417L9.66602 29.415C8.46362 30.1022 7.1055 29.5635 6.47559 28.5137L6.45508 28.4814L6.43848 28.4473C6.18497 27.9403 6.19922 27.4407 6.19922 27.1992V27.1123L6.21484 27.0264L7.45605 19.9463L2.36816 14.9824L2.3584 14.9736L2.35938 14.9727C1.4742 14.0874 1.43866 12.7368 2.25 11.8125C2.60619 11.2361 3.20538 11.0267 3.7627 11.0039L10.7959 10.0166L13.8984 3.56641L13.918 3.52441L13.9414 3.48535Z" fill="var(--sky-blue)" stroke="black" stroke-width="2"/>
        </svg>
    </div>
    <div class="content-area">
        {#if currentPhase === 3 || hasUserAnswered}
            <H2>{currentCard.vocabCard.word}</H2>
            <P1 >{currentCard.vocabCard.definition}</P1>
            {#if hasUserAnswered}
                <H5>Did you guess correctly?</H5>
            {/if}
        {:else if currentPhase === 1}
            <P1>{@html safeHint1}</P1>
        {:else if currentPhase === 2}
            <P1>{@html safeHint2}</P1>
        {:else if currentPhase === 0}
            <H2>{currentCard.vocabCard.word}</H2>
        {/if}
    </div>
</div>

<style>
    /* Main container styling */
    .card-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        max-width: 48rem;
        box-shadow: 0.625rem 0.625rem 0 0 black;
        border-radius: 0 1rem 0 0;
        position: relative;
    }

    /* Navigation bar styling */
    .navigation-bar {
        display: flex;
        gap: 0.625rem;
        align-items: center;
        justify-content: end;
        padding: 0.5rem 1rem;
        width: 100%;
        background-color: var(--aquamarine);
        border-radius: 0.75rem;
        border: 0.1875rem solid black;
        z-index: 10;
    }
    /* Content area styling */
    .content-area {
        padding: 2rem;
        margin-top: -1.25rem;
        display: flex;
        flex-direction: column;
        gap: 2rem;
        justify-content: center;
        align-items: center;
        width: 100%;
        min-height: 20rem;
        background-color: white;
        border: 0.1875rem solid black;
        font-family: 'Open Sans', sans-serif;
        text-align: center;
    }

    mark {
        background-color: var(--orange) !important;
    }

    /* Responsive styling */
    @media (max-width: 768px) {
        .content-area {
            padding: 1.5rem;
        }
    }
</style> 