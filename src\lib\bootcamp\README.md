# Bootcamp System

Structured SAT preparation course with video lectures, interactive notes, and additional resources integrated with Contentful CMS.

## Overview

The bootcamp system delivers comprehensive SAT preparation through organized video lectures, interactive note-taking, and supplementary materials. Content is managed via Contentful CMS and requires "Bootcamp" or "Pro" subscription access.

## Architecture

```
bootcamp/
├── index.js                 # Exports Note and NavContent components
├── NavContent.svelte       # Navigation for bootcamp sections
├── Note.svelte             # Interactive notes with practice questions
├── NoteHyper.svelte        # Enhanced notes with hyperlink support
├── allLectures.js          # Video lecture registry
├── allAdditionalResources.js # Supplementary materials registry
└── notesData/              # Individual lesson note files (0.js - 10.js)
    ├── 0.js                # Lecture 0 notes
    ├── 1.js                # Lecture W1 notes
    └── ...                 # Additional lectures
```

## Core Components

### Navigation System
```svelte
<!-- NavContent.svelte - Bootcamp navigation -->
<script>
  import { NavContent } from '$lib/bootcamp';
  
  let currentSection = 'lectures'; // 'lectures' | 'notes' | 'resources'
</script>

<NavContent 
  section={currentSection}
  onSectionChange={(section) => currentSection = section}
/>
```

### Interactive Notes
```svelte
<!-- Note.svelte - Practice questions within notes -->
<script>
  import { Note } from '$lib/bootcamp';
  
  let noteData = {
    title: "Transitions & Students' Notes",
    notes: "Content with embedded practice...",
    answers: [
      { question: "Which transition fits?", choices: ["A", "B", "C", "D"], correct: 0 }
    ],
    hyperlink: "/bootcamp/lectures/1",
    slug: "1"
  };
</script>

<Note 
  title={noteData.title}
  notes={noteData.notes}
  answers={noteData.answers}
  hyperlink={noteData.hyperlink}
  slug={noteData.slug}
/>
```

## Data Structure

### Lecture Object
```javascript
// allLectures.js structure
const lecture = {
  slug: '1',                    // URL slug
  title: 'Lecture W1: Transitions & Students\' Notes',
  video: '<iframe src="...">',  // Embedded Vimeo iframe
  hasAR: true                   // Has additional resources
};
```

### Note Data
```javascript
// notesData/1.js structure
export const notes = {
  title: "Lecture W1: Transitions",
  content: `
    <h3>Key Concepts</h3>
    <p>Transition words connect ideas...</p>
    <practice-question id="1">
      Which transition best connects these sentences?
    </practice-question>
  `,
  practiceQuestions: [
    {
      id: 1,
      question: "Choose the best transition:",
      choices: ["However", "Therefore", "Meanwhile", "Furthermore"],
      correct: 1,
      explanation: "Therefore shows cause and effect..."
    }
  ]
};
```

### Additional Resources
```javascript
// allAdditionalResources.js structure
const resource = {
  slug: 'transitions-practice',
  title: 'Transitions Practice Set',
  type: 'worksheet',          // 'worksheet' | 'video' | 'quiz'
  url: '/resources/transitions.pdf',
  lectureSlug: '1'           // Associated lecture
};
```

## Content Management

### Contentful Integration
```typescript
// In server-side routes
import { client } from '$lib/server/contentful';

// Fetch lecture content
const lectures = await client.getEntries({
  content_type: 'bootcampLecture',
  order: 'fields.order'
});

// Fetch additional resources
const resources = await client.getEntries({
  content_type: 'additionalResource',
  'fields.lectureSlug': lectureSlug
});
```

## Route Integration

### Lecture Pages
```svelte
<!-- /bootcamp/lectures/[slug]/+page.svelte -->
<script>
  import { allLectures } from '$lib/bootcamp/allLectures.js';
  
  let { data } = $props(); // From +page.server.js
  let lecture = allLectures.find(l => l.slug === data.slug);
</script>

<div class="lecture-container">
  <h1>{lecture.title}</h1>
  <div class="video-wrapper">
    {@html lecture.video}
  </div>
</div>
```

### Notes Pages
```svelte
<!-- /bootcamp/notes/[slug]/+page.svelte -->
<script>
  import { Note } from '$lib/bootcamp';
  
  let { data } = $props();
  let noteData = await import(`$lib/bootcamp/notesData/${data.slug}.js`);
</script>

<Note {...noteData} slug={data.slug} />
```

## Access Control

### Role-based Access
```typescript
// In +layout.server.ts or hooks.server.ts
export const load = async ({ locals }) => {
  const user = locals.user;
  
  if (!user?.customClaims?.roles?.includes('Bootcamp') && 
      !user?.customClaims?.roles?.includes('Pro')) {
    throw redirect(302, '/pricing');
  }
  
  return { user };
};
```

## Interactive Features

### Practice Questions in Notes
```svelte
<!-- Within Note.svelte -->
<script>
  let selections = $state(new Array(answers?.length).fill(null));
  let checked = $state(new Array(answers?.length).fill([]));
  let prompts = $state([]);
  
  function check(id) {
    if (selections[id] === null || checked[id].includes(selections[id])) {
      prompts[id] = 'empty';
      return;
    }
    
    checked[id] = [...checked[id], selections[id]];
    
    // Check if answer is correct
    if (selections[id] === answers[id].correct) {
      prompts[id] = 'correct';
    } else {
      prompts[id] = 'incorrect';
    }
  }
</script>

{#each answers as answer, i}
  <div class="practice-question">
    <p>{answer.question}</p>
    
    {#each answer.choices as choice, j}
      <label>
        <input 
          type="radio" 
          bind:group={selections[i]} 
          value={j} 
        />
        {choice}
      </label>
    {/each}
    
    <button onclick={() => check(i)}>Check Answer</button>
    
    {#if prompts[i] === 'correct'}
      <p class="success">Correct! {answer.explanation}</p>
    {:else if prompts[i] === 'incorrect'}
      <p class="error">Incorrect. {answer.explanation}</p>
    {/if}
  </div>
{/each}
```

## Content Types

### Video Lectures
- **Overview** - Course introduction and structure
- **Writing (W1-W4)** - Grammar, punctuation, transitions, style
- **Reading (R1-R4)** - Comprehension, vocabulary, analysis
- **Math (M1-M4)** - Algebra, geometry, data analysis

### Study Materials
- **Interactive Notes** - Key concepts with embedded practice
- **Additional Resources** - Worksheets, practice sets, references
- **Hyperlinked Content** - Cross-references between topics

## Progress Tracking

### Lecture Completion
```typescript
// Track lecture completion
import { incrementMissionProgress } from '$lib/missions';

const markLectureComplete = async (userId: string, lectureSlug: string) => {
  // Update user progress
  await incrementMissionProgress(userId, 'lecture_completed', 1);
  
  // Store completion in user profile
  await updateDoc(doc(db, 'users', userId), {
    [`bootcampProgress.lectures.${lectureSlug}`]: {
      completed: true,
      completedAt: new Date()
    }
  });
};
```

## Development Tips

1. **Content Updates**: Use Contentful webhook to refresh content cache
2. **Video Embedding**: Vimeo Pro for better video management and analytics
3. **Mobile Optimization**: Responsive video players and touch-friendly notes
4. **Progress Persistence**: Store note interactions and video watch time
5. **Offline Support**: Cache critical content for offline study
6. **Analytics**: Track engagement with PostHog for content optimization