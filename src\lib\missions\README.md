# Missions System

A gamification system that encourages daily study habits through missions, streaks, and rewards.

## Overview

The missions system tracks user engagement through daily challenges and maintains streak counters to motivate consistent study habits. It integrates with PostHog for analytics and Firebase for data persistence.

## Architecture

```
missions/
├── index.ts           # Central exports
├── missionCatalog.ts  # Mission definitions and utilities  
├── missionEngine.ts   # Core business logic
└── mission.store.ts   # Svelte stores and reactivity
```

## Core Concepts

### Mission Types
- **Daily Questions** - Answer 5 practice questions (100 points)
- **Daily Vocab** - Complete 1 vocabulary session (50 points)  
- **Morning Study** - Login between 9 AM - 4 PM (25 points)
- **Evening Study** - Login between 7 PM - 11 PM (25 points)

### Streak System
- Tracks consecutive days of completing all daily missions
- Resets if user misses a day
- Milestone rewards at 3, 7, 30, and 100+ day streaks

## Key Functions

### Mission Catalog (`missionCatalog.ts`)
```typescript
// Get all daily missions
const missions = getDailyMissions();

// Find specific mission
const mission = getMissionById('daily_questions');

// Get missions by metric type
const questionMissions = getMissionsByMetric(MissionMetric.QUESTIONS_ANSWERED);
```

### Mission Engine (`missionEngine.ts`)
```typescript
// Initialize today's progress
await initializeDailyProgress(userId);

// Update mission progress
await incrementMissionProgress(userId, MissionMetric.QUESTIONS_ANSWERED, 1);

// Check if all missions complete
const complete = checkAllMissionsComplete(progress);
```

### Reactive Stores (`mission.store.ts`)
```typescript
// Subscribe to mission progress
import { missionProgress, streakData, allMissionsComplete } from '$lib/missions';

// Get completion percentages
import { missionCompletionPercentages } from '$lib/missions';
```

## Data Structure

### DailyMissionProgress
```typescript
{
  missions: {
    'daily_questions': 3,    // Current progress
    'daily_vocab': 1,        // Target: 1, Complete!
    'morning_login': 0,      // Not started
    'evening_login': 1       // Complete!
  },
  completed: false,          // All missions complete?
  createdAt: Date
}
```

### UserStreakData
```typescript
{
  currentStreak: 7,          // Days in current streak
  longestStreak: 15,         // Personal best
  lastMissionDate: '2024-01-15',
  streakFrozen: false        // Streak protection (future feature)
}
```

## Integration Points

### With Dashboard
```svelte
<!-- DailyMissions.svelte -->
<script>
  import { missionProgress, missionCompletionPercentages } from '$lib/missions';
</script>

{#each $dailyMissions as mission}
  <MissionCard 
    {mission} 
    progress={$missionCompletionPercentages[mission.id]} 
  />
{/each}
```

### With Study Tools
```typescript
// In question bank, vocab tool, etc.
import { incrementMissionProgress } from '$lib/missions';

// When user completes an action
await incrementMissionProgress(userId, MissionMetric.QUESTIONS_ANSWERED, 1);
```

### With PostHog Analytics
```typescript
// Automatic tracking in missionEngine.ts
posthog.capture('mission_completed', {
  missionId: mission.id,
  streak: currentStreak
});

posthog.capture('streak_milestone', {
  streak: newStreak,
  milestone: true
});
```

## Development Tips

1. **Adding New Missions**: Update `MISSION_CATALOG` in `missionCatalog.ts`
2. **New Metrics**: Add to `MissionMetric` enum in types
3. **Testing**: Mock Firestore and PostHog for unit tests
4. **Performance**: Stores auto-subscribe/unsubscribe based on auth state

## Common Usage Patterns

```typescript
// Check if user can access a feature
if ($allMissionsComplete) {
  // Show bonus content or rewards
}

// Display progress indicators
const percentage = $missionCompletionPercentages['daily_questions']; // 0-100

// Handle mission completion
onMount(() => {
  const unsubscribe = subscribeMissionProgress(userId);
  return unsubscribe;
});
```