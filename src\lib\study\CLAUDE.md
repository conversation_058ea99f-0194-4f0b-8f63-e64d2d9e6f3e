# study/

Study area navigation and layout components for authenticated user study dashboard.

## Components
- **NavBar.svelte** - Study area navigation bar with links to question bank, mock tests, vocabulary tool, and bootcamp

**Features**:
- Role-based navigation (shows/hides sections based on user subscription)
- Active route highlighting for current study tool
- Integration with authentication state for user-specific features
- Responsive design for mobile and desktop study sessions

**Access Control**: Requires "Pro" role for full study area access
**Routes**: Used in /study/* layout for navigation between study tools
**Integration**: Works with Firebase auth state and role management

**No index.ts**: Single navigation component imported directly