<script>
    import { H2, P1, P2, But<PERSON> } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    import SignUpButton from '$lib/landingPage/SignUpButton.svelte';
    
    let { 
        sectionLabel = "DSAT16 Demo",
        headline = "See DSAT16 in action",
        subheadline = "Click play to with this 2-minute demo",
        primaryButton = "Get Started",
        secondaryButton = "Button",
        videoPlaceholder = "VIDEO DEMO"
    } = $props();
</script>

<SectionWrapper --bg-color="var(--light-yellow)" --padding-top="8rem" --padding-bottom="8rem">
<div class="demo-section">
    <div class="demo-content">
        <P2>{@html sectionLabel}</P2>
        <H2>{@html headline}</H2>
        <P1>{@html subheadline}</P1>

        <div class="demo-buttons">
            <SignUpButton>{primaryButton}</SignUpButton>
            <!-- <Button isSecondary>{secondaryButton}</Button> -->
        </div>
    </div>

    <div class="demo-video">
        <div class="video-placeholder">
            <div class="play-button">▶</div>
            <P2>{videoPlaceholder}</P2>
        </div>
    </div>
</div>
</SectionWrapper>

<style>
    /* Demo Section */
    .demo-section {
        display: flex;
        flex-direction: column;
        gap: 4rem;
        align-items: center;
                width: 100%;
    }
    
    .demo-content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        text-align: center;
        width: 100%;
    }
    
    .demo-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .demo-video {
        display: flex;
        justify-content: center;
        width: 100%;
    }
    
    .video-placeholder {
        width: 100%;
        height: 18.75rem;
        background: var(--pitch-black);
        color: white;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border: 0.25rem solid var(--pitch-black);
        font-weight: 700;
        gap: 1rem;
        position: relative;
        cursor: pointer;
    }
    
    .play-button {
        background: var(--yellow);
        color: var(--pitch-black);
        width: 4rem;
        height: 4rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        border: 0.1875rem solid var(--pitch-black);
    }
    
    .video-placeholder:hover .play-button {
        transform: scale(1.1);
    }
    
    /* Mobile Responsiveness */
    @media (max-width: 48rem) {
        .demo-section {
            gap: 2rem;
        }

        .video-placeholder {
            height: 12.5rem;
        }
    }
</style>
