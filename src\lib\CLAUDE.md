# lib/

Core library containing all reusable components, utilities, and business logic for the SAT preparation platform.

## Architecture Overview
SvelteKit 5 application with modular component architecture, Firebase backend, and external service integrations.

## Module Organization

### **Core Features**
- **analysis/** - Post-test analysis and score visualization
- **mockTest/** - Adaptive test simulation engine with real-time scoring  
- **questionBank/** - Individual question practice system
- **vocabTool/** - Spaced repetition vocabulary learning
- **bootcamp/** - Structured course content and navigation

### **Infrastructure**
- **firebase/** - Authentication, Firestore, and user data management
- **server/** - Backend service integrations (Supabase, Contentful, AI, Redis, Stripe)
- **types/** - TypeScript definitions for all data structures
- **stores.js** - Global Svelte stores (finalAnswers, finalMarked for test state)
- **utilities.ts** - Helper functions (debounce utility)

### **User Interface**  
- **ui/** - Reusable component library with typography, forms, and specialized widgets
- **landingPage/** - Marketing website components and conversion flows
- **dashboard/** - User progress widgets and gamification elements
- **study/** - Study area navigation and layout

### **Gamification & Business Logic**
- **missions/** - Daily challenge system with streak tracking and PostHog analytics
- **stripe/** - Subscription management and payment processing  
- **annotate/** - Question annotation system for test-taking

### **Static Assets**
- **assets/** - Images and visual content for marketing and UI

## Key Technical Patterns
- **Svelte 5 Runes**: `$state`, `$derived`, `$effect` for reactive state management
- **Role-based Access**: Firebase custom claims ("Pro", "Bootcamp", "Pro+Bootcamp")
- **Real-time Data**: Firestore listeners for live progress updates
- **TypeScript**: Full type coverage with auto-generated database schemas
- **Modular Exports**: Each module has index.ts for clean imports

## Service Integration
**Firebase**: Auth, Firestore, hosting (asia-southeast1)
**Supabase**: Question bank and structured content  
**Contentful**: CMS for bootcamp materials
**PostHog**: Analytics and error tracking
**AI**: Google Generative AI for content generation