# Stripe Payment Integration

Secure payment processing and subscription management system integrating Stripe with Firebase authentication and role-based access control.

## Overview

The Stripe integration handles subscription payments, manages user roles based on payment status, and processes webhook events for real-time subscription updates. It supports multiple subscription tiers and integrates with Firebase custom claims for access control.

## Architecture

```
stripe/
└── stripe.ts    # Stripe SDK integration and payment processing
```

## Subscription Tiers

### Available Plans
- **Pro** - Full access to mock tests, question bank, and analytics
- **Bootcamp** - Access to structured course content and lectures  
- **Pro+Bootcamp** - Complete access to all features

### Role-based Access Control
```typescript
// User roles managed through Firebase custom claims
interface UserRoles {
  roles: ('Pro' | 'Bootcamp' | 'Pro+Bootcamp')[];
}

// Check subscription access
function hasProAccess(user: any): boolean {
  return user?.customClaims?.roles?.includes('Pro') || 
         user?.customClaims?.roles?.includes('Pro+Bootcamp');
}

function hasBootcampAccess(user: any): boolean {
  return user?.customClaims?.roles?.includes('Bootcamp') || 
         user?.customClaims?.roles?.includes('Pro+Bootcamp');
}
```

## Payment Processing

### Stripe Client Setup
```typescript
// stripe.ts - Stripe integration
import Stripe from 'stripe';

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2024-06-20',
  appInfo: {
    name: 'DSAT16',
    version: '1.0.0',
  }
});

// Product and price configuration
export const STRIPE_PRODUCTS = {
  pro: {
    monthly: 'price_pro_monthly_id',
    yearly: 'price_pro_yearly_id',
  },
  bootcamp: {
    monthly: 'price_bootcamp_monthly_id',
    yearly: 'price_bootcamp_yearly_id',
  },
  pro_bootcamp: {
    monthly: 'price_pro_bootcamp_monthly_id',
    yearly: 'price_pro_bootcamp_yearly_id',
  }
} as const;
```

### Checkout Session Creation
```typescript
// Create Stripe checkout session
export async function createCheckoutSession(
  priceId: string,
  userId: string,
  userEmail: string,
  successUrl: string,
  cancelUrl: string
): Promise<Stripe.Checkout.Session> {
  
  const session = await stripe.checkout.sessions.create({
    customer_email: userEmail,
    client_reference_id: userId, // Link to Firebase user
    mode: 'subscription',
    payment_method_types: ['card'],
    line_items: [
      {
        price: priceId,
        quantity: 1,
      },
    ],
    success_url: successUrl,
    cancel_url: cancelUrl,
    allow_promotion_codes: true,
    billing_address_collection: 'required',
    metadata: {
      firebase_uid: userId,
      plan_type: getPlanTypeFromPrice(priceId),
    },
  });

  return session;
}

// Helper to determine plan type
function getPlanTypeFromPrice(priceId: string): string {
  if (Object.values(STRIPE_PRODUCTS.pro).includes(priceId)) return 'Pro';
  if (Object.values(STRIPE_PRODUCTS.bootcamp).includes(priceId)) return 'Bootcamp';
  if (Object.values(STRIPE_PRODUCTS.pro_bootcamp).includes(priceId)) return 'Pro+Bootcamp';
  return 'unknown';
}
```

### API Route Implementation
```typescript
// routes/api/stripe/create-checkout/+server.ts
import { json, error } from '@sveltejs/kit';
import { createCheckoutSession, STRIPE_PRODUCTS } from '$lib/stripe';

export async function POST({ request, locals }) {
  const user = locals.user;
  
  if (!user) {
    throw error(401, 'Authentication required');
  }

  const { planType, billingCycle } = await request.json();
  
  // Validate plan selection
  if (!STRIPE_PRODUCTS[planType] || !STRIPE_PRODUCTS[planType][billingCycle]) {
    throw error(400, 'Invalid plan selection');
  }

  try {
    const priceId = STRIPE_PRODUCTS[planType][billingCycle];
    
    const session = await createCheckoutSession(
      priceId,
      user.uid,
      user.email,
      `${url.origin}/thank-you`,
      `${url.origin}/pricing`
    );

    return json({ 
      sessionId: session.id,
      url: session.url 
    });

  } catch (err) {
    console.error('Checkout session creation failed:', err);
    throw error(500, 'Failed to create checkout session');
  }
}
```

## Webhook Processing

### Event Handling
```typescript
// routes/api/stripe/webhook/+server.ts
import { text, json } from '@sveltejs/kit';
import { stripe } from '$lib/stripe';
import { adminAuth } from '$lib/server/admin';

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET!;

export async function POST({ request }) {
  const body = await request.text();
  const signature = request.headers.get('stripe-signature')!;

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(body, signature, endpointSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return text('Webhook signature verification failed', { status: 400 });
  }

  // Process different webhook events
  switch (event.type) {
    case 'checkout.session.completed':
      await handleCheckoutCompleted(event.data.object);
      break;
      
    case 'invoice.payment_succeeded':
      await handlePaymentSucceeded(event.data.object);
      break;
      
    case 'invoice.payment_failed':
      await handlePaymentFailed(event.data.object);
      break;
      
    case 'customer.subscription.updated':
      await handleSubscriptionUpdated(event.data.object);
      break;
      
    case 'customer.subscription.deleted':
      await handleSubscriptionCancelled(event.data.object);
      break;

    default:
      console.log(`Unhandled event type: ${event.type}`);
  }

  return json({ received: true });
}
```

### Subscription Event Handlers
```typescript
// Handle successful checkout
async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  const userId = session.client_reference_id!;
  const planType = session.metadata?.plan_type;

  if (!planType) {
    console.error('No plan type in session metadata');
    return;
  }

  try {
    // Update Firebase user roles
    const roles = [planType];
    await adminAuth.setCustomUserClaims(userId, { roles });

    // Save subscription info to Firestore
    await adminDB.collection('users').doc(userId).update({
      stripeCustomerId: session.customer,
      subscriptionStatus: 'active',
      subscriptionType: planType,
      subscriptionStarted: new Date(),
      updatedAt: new Date()
    });

    console.log(`User ${userId} upgraded to ${planType}`);

  } catch (error) {
    console.error('Failed to process successful checkout:', error);
  }
}

// Handle subscription updates
async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  const customerId = subscription.customer as string;
  
  // Find user by Stripe customer ID
  const userQuery = await adminDB
    .collection('users')
    .where('stripeCustomerId', '==', customerId)
    .limit(1)
    .get();

  if (userQuery.empty) {
    console.error('No user found for customer:', customerId);
    return;
  }

  const userId = userQuery.docs[0].id;
  const newStatus = subscription.status;
  
  try {
    // Update subscription status
    await adminDB.collection('users').doc(userId).update({
      subscriptionStatus: newStatus,
      updatedAt: new Date()
    });

    // Remove roles if subscription is cancelled or past due
    if (newStatus === 'canceled' || newStatus === 'past_due') {
      await adminAuth.setCustomUserClaims(userId, { roles: [] });
    }

  } catch (error) {
    console.error('Failed to update subscription:', error);
  }
}

// Handle subscription cancellation
async function handleSubscriptionCancelled(subscription: Stripe.Subscription) {
  const customerId = subscription.customer as string;
  
  const userQuery = await adminDB
    .collection('users')
    .where('stripeCustomerId', '==', customerId)
    .limit(1)
    .get();

  if (userQuery.empty) return;

  const userId = userQuery.docs[0].id;

  try {
    // Remove all subscription roles
    await adminAuth.setCustomUserClaims(userId, { roles: [] });

    // Update Firestore
    await adminDB.collection('users').doc(userId).update({
      subscriptionStatus: 'cancelled',
      subscriptionCancelled: new Date(),
      updatedAt: new Date()
    });

  } catch (error) {
    console.error('Failed to handle subscription cancellation:', error);
  }
}
```

## Customer Portal

### Subscription Management
```typescript
// Create customer portal session for subscription management
export async function createPortalSession(
  customerId: string,
  returnUrl: string
): Promise<Stripe.BillingPortal.Session> {
  
  const portalSession = await stripe.billingPortal.sessions.create({
    customer: customerId,
    return_url: returnUrl,
  });

  return portalSession;
}

// API route for customer portal
// routes/api/stripe/portal/+server.ts
export async function POST({ request, locals }) {
  const user = locals.user;
  
  if (!user) {
    throw error(401, 'Authentication required');
  }

  // Get user's Stripe customer ID
  const userDoc = await adminDB.collection('users').doc(user.uid).get();
  const userData = userDoc.data();

  if (!userData?.stripeCustomerId) {
    throw error(400, 'No active subscription found');
  }

  try {
    const portalSession = await createPortalSession(
      userData.stripeCustomerId,
      `${url.origin}/study`
    );

    return json({ url: portalSession.url });

  } catch (err) {
    console.error('Portal session creation failed:', err);
    throw error(500, 'Failed to create portal session');
  }
}
```

## Frontend Integration

### Checkout Flow
```svelte
<!-- Subscription component -->
<script>
  import { loadStripe } from '@stripe/stripe-js';
  import { user } from '$lib/firebase/auth.svelte';
  
  const stripePromise = loadStripe(process.env.PUBLIC_STRIPE_PUBLISHABLE_KEY!);
  
  let loading = $state(false);
  
  async function handleSubscription(planType: string, billingCycle: string) {
    loading = true;
    
    try {
      // Create checkout session
      const response = await fetch('/api/stripe/create-checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ planType, billingCycle })
      });
      
      const { sessionId } = await response.json();
      
      // Redirect to Stripe Checkout
      const stripe = await stripePromise;
      await stripe?.redirectToCheckout({ sessionId });
      
    } catch (error) {
      console.error('Checkout failed:', error);
      alert('Payment failed. Please try again.');
    } finally {
      loading = false;
    }
  }
  
  async function openCustomerPortal() {
    try {
      const response = await fetch('/api/stripe/portal', { method: 'POST' });
      const { url } = await response.json();
      
      window.location.href = url;
    } catch (error) {
      console.error('Portal failed:', error);
    }
  }
</script>

<div class="subscription-plans">
  <div class="plan-card">
    <h3>Pro Plan</h3>
    <p>$19.99/month</p>
    
    <button 
      onclick={() => handleSubscription('pro', 'monthly')}
      disabled={loading}
    >
      {loading ? 'Processing...' : 'Subscribe to Pro'}
    </button>
  </div>
  
  <!-- More plan cards... -->
  
  {#if $user?.customClaims?.roles?.length > 0}
    <button onclick={openCustomerPortal}>
      Manage Subscription
    </button>
  {/if}
</div>
```

## Security Considerations

### Webhook Security
```typescript
// Verify webhook signatures
function verifyWebhookSignature(
  body: string, 
  signature: string, 
  secret: string
): boolean {
  try {
    stripe.webhooks.constructEvent(body, signature, secret);
    return true;
  } catch {
    return false;
  }
}

// Rate limiting for webhook endpoints
export async function rateLimitWebhook(request: Request): Promise<boolean> {
  const ip = request.headers.get('x-forwarded-for') || 'unknown';
  const key = `webhook_rate_limit:${ip}`;
  
  const current = await redis.incr(key);
  if (current === 1) {
    await redis.expire(key, 60); // 1 minute window
  }
  
  return current <= 100; // Max 100 webhook calls per minute per IP
}
```

### Payment Data Handling
- Never store credit card information directly
- Use Stripe customer IDs for recurring billing
- Implement proper error handling for failed payments
- Log all payment events for audit trails
- Validate all webhook events server-side

## Testing

### Test Mode Configuration
```typescript
// Use test API keys for development
const stripe = new Stripe(
  process.env.NODE_ENV === 'production' 
    ? process.env.STRIPE_SECRET_KEY! 
    : process.env.STRIPE_TEST_SECRET_KEY!,
  { apiVersion: '2024-06-20' }
);

// Test webhook with Stripe CLI
// stripe listen --forward-to localhost:5173/api/stripe/webhook
```

## Development Tips

1. **Testing**: Use Stripe test mode and CLI for webhook testing
2. **Error Handling**: Implement comprehensive error handling for payment failures
3. **Logging**: Log all payment events and webhook processing
4. **Security**: Always verify webhook signatures and implement rate limiting
5. **User Experience**: Provide clear feedback during payment processing
6. **Compliance**: Ensure PCI compliance and data protection standards
7. **Monitoring**: Set up alerts for failed payments and webhook errors